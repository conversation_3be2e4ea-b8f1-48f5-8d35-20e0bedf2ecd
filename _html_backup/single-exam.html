<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAT Mock Test Series - 2024 - DRX Pharma Academy</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Exam Window Styles */
        .exam-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .exam-header {
            background: #ffffff;
            border-radius: 12px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            border: 1px solid #e2e8f0;
        }

        .exam-header-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .exam-title {
            font-size: 30px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 8px;
            display: block;
            line-height: 1.3;
            letter-spacing: -0.025em;
        }

        .exam-subtitle {
            color: #4a5568;
            font-size: 16px;
            margin-bottom: 35px;
            display: block;
            line-height: 1.5;
            font-weight: 400;
        }


        .exam-info-bar {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 25px;
            flex-wrap: wrap;
        }

        .info-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            color: #2d3748;
            background: #f7fafc;
            padding: 15px 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            min-width: 140px;
            justify-content: center;
        }

        .info-item:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            transform: translateY(-1px);
        }

        .info-item i {
            color: #667eea;
            font-size: 20px;
        }

        .info-item span {
            font-weight: 600;
            font-size: 14px;
            color: #2d3748;
        }

        .exam-window {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .exam-nav {
            background: #f8f9ff;
            padding: 20px 30px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .question-progress {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
        }

        .question-number {
            font-weight: 600;
            color: #333;
            font-size: 16px;
            white-space: nowrap;
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .progress-fill {
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .timer {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #333;
            background: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .timer i {
            color: #667eea;
            font-size: 14px;
        }

        .timer span {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 15px;
            font-weight: 500;
        }

        .question-content {
            padding: 40px;
        }

        .question-text {
            font-size: 18px;
            color: #333;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .option-item {
            margin-bottom: 15px;
        }

        .option-item label {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            line-height: 1.5;
        }

        .option-item label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .option-item input[type="radio"] {
            display: none;
        }

        .option-item input[type="radio"]:checked + label {
            border-color: #667eea;
            background: #f8f9ff;
            color: #667eea;
        }

        .exam-controls {
            padding: 30px 40px;
            background: #f8f9ff;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 14px 28px;
            border-radius: 10px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-previous {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-previous:hover {
            background: #e2e8f0;
        }

        .btn-next {
            background: #667eea;
            color: white;
        }

        .btn-next:hover {
            background: #5a67d8;
        }

        .btn-submit {
            background: #10b981;
            color: white;
        }

        .btn-submit:hover {
            background: #059669;
        }

        .question-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 13px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #666;
            border-radius: 6px;
            cursor: pointer;
        }

        .btn-small:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .question-palette {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .palette-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }

        .palette-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 10px;
            justify-items: center;
        }

        .palette-item {
            width: 42px;
            height: 42px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .palette-item.current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .palette-item.answered {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }


        .palette-legend {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            font-size: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
        }

        @media (max-width: 768px) {
            .exam-info-bar {
                flex-direction: column;
                gap: 10px;
            }

            .question-content {
                padding: 20px;
            }

            .exam-controls {
                padding: 20px;
                flex-direction: column;
                gap: 15px;
            }

            .palette-grid {
                grid-template-columns: repeat(5, 1fr);
            }

            .palette-legend {
                flex-direction: column;
                gap: 8px;
            }
        }

        /* Results Display Styles */
        .results-container {
            display: none;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 20px;
        }

        .results-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .results-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .results-header-content {
            position: relative;
            z-index: 1;
        }

        .results-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .results-score {
            font-size: 56px;
            font-weight: 800;
            margin: 25px 0;
            text-shadow: 0 2px 8px rgba(0,0,0,0.3);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }

        .results-percentage {
            font-size: 20px;
            opacity: 0.95;
            font-weight: 500;
            background: rgba(255,255,255,0.1);
            padding: 8px 20px;
            border-radius: 25px;
            display: inline-block;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }

        .results-progress {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            margin-bottom: 8px;
            color: rgba(255,255,255,0.9);
        }

        .progress-container {
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            height: 8px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill-correct {
            background: linear-gradient(90deg, #10b981, #34d399);
            height: 100%;
            border-radius: 25px;
            transition: width 1s ease-in-out;
        }

        .progress-fill-incorrect {
            background: linear-gradient(90deg, #ef4444, #f87171);
            height: 100%;
            border-radius: 25px;
            transition: width 1s ease-in-out;
            position: absolute;
            top: 0;
            left: 0;
        }

        .results-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 25px;
            background: #f8f9ff;
        }

        .stat-item {
            text-align: center;
            padding: 20px 15px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #e2e8f0;
            transition: background 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .stat-item.correct {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border-color: #bbf7d0;
        }

        .stat-item.correct::before {
            background: linear-gradient(90deg, #10b981, #34d399);
        }

        .stat-item.correct .stat-number {
            color: #047857;
        }

        .stat-item.correct .stat-label {
            color: #065f46;
        }

        .stat-item.incorrect {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border-color: #fecaca;
        }

        .stat-item.incorrect::before {
            background: linear-gradient(90deg, #ef4444, #f87171);
        }

        .stat-item.incorrect .stat-number {
            color: #dc2626;
        }

        .stat-item.incorrect .stat-label {
            color: #991b1b;
        }

        .stat-item.unanswered {
            background: linear-gradient(135deg, #fffbeb, #fef3c7);
            border-color: #fed7aa;
        }

        .stat-item.unanswered::before {
            background: linear-gradient(90deg, #f59e0b, #fbbf24);
        }

        .stat-item.unanswered .stat-number {
            color: #d97706;
        }

        .stat-item.unanswered .stat-label {
            color: #92400e;
        }

        .stat-item.time {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-color: #cbd5e0;
        }

        .stat-item.time::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-item.time .stat-number {
            color: #4c51bf;
        }

        .stat-item.time .stat-label {
            color: #553c9a;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }

        .stat-label {
            font-size: 14px;
            font-weight: 600;
        }

        .results-review {
            padding: 30px;
        }

        .review-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .question-review {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }

        .question-review.correct {
            border-color: #10b981;
            background: #f0fdfa;
        }

        .question-review.incorrect {
            border-color: #ef4444;
            background: #fef2f2;
        }

        .question-review.unanswered {
            border-color: #f59e0b;
            background: #fffbf0;
        }

        .review-question-text {
            font-weight: 500;
            margin-bottom: 15px;
            color: #333;
        }

        .review-options {
            margin-bottom: 15px;
        }

        .review-option {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-size: 14px;
        }

        .review-option.user-answer {
            background: #dbeafe;
            border: 1px solid #3b82f6;
        }

        .review-option.correct-answer {
            background: #dcfce7;
            border: 1px solid #10b981;
            font-weight: 500;
        }

        .review-option.wrong-answer {
            background: #fee2e2;
            border: 1px solid #ef4444;
        }

        .review-explanation {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
            font-size: 14px;
            line-height: 1.5;
        }

        .results-actions {
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-retake,
        .btn-home {
            padding: 14px 28px;
            margin: 0;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 16px;
            min-height: 48px;
            min-width: 140px;
        }

        .btn-retake {
            background: #667eea;
            color: white;
        }

        .btn-retake:hover {
            background: #5a67d8;
        }

        .btn-home {
            background: #e2e8f0;
            color: #333;
        }

        .btn-home:hover {
            background: #cbd5e0;
        }

        @media (max-width: 768px) {
            .exam-container {
                margin: 20px auto;
                padding: 0 15px;
            }

            .exam-header {
                padding: 25px 20px;
                margin-bottom: 20px;
            }

            .exam-title {
                font-size: 24px;
                margin-bottom: 6px;
            }

            .exam-subtitle {
                font-size: 15px;
                margin-bottom: 20px;
            }


            .exam-info-bar {
                flex-direction: row;
                gap: 8px;
                margin-top: 20px;
                justify-content: space-between;
            }

            .info-item {
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 12px 8px;
                min-width: auto;
                flex: 1;
                max-width: none;
                margin: 0;
                gap: 6px;
            }

            .info-item i {
                font-size: 16px;
            }

            .info-item span {
                font-size: 12px;
                text-align: center;
                font-weight: 600;
            }

            .exam-nav {
                flex-direction: row;
                gap: 10px;
                text-align: center;
                padding: 12px 20px;
                justify-content: space-between;
                align-items: center;
            }

            .question-progress {
                justify-content: flex-start;
                gap: 10px;
                flex: 1;
            }

            .question-number {
                font-size: 14px;
                min-width: fit-content;
            }

            .progress-bar {
                width: 100px;
                height: 6px;
                flex-shrink: 0;
            }

            .timer {
                padding: 6px 12px;
                font-size: 14px;
                white-space: nowrap;
            }

            .timer i {
                font-size: 12px;
            }

            .timer span {
                font-size: 13px;
            }

            .question-content {
                padding: 20px;
            }

            .question-text {
                font-size: 16px;
                line-height: 1.5;
            }

            .options-list {
                margin-top: 20px;
            }

            .option-item {
                margin-bottom: 15px;
            }

            .option-item label {
                font-size: 15px;
                line-height: 1.4;
            }

            .exam-controls {
                flex-direction: column;
                gap: 15px;
                padding: 15px 20px;
            }

            .nav-buttons {
                justify-content: center;
                gap: 10px;
            }

            .btn {
                padding: 12px 24px;
                font-size: 15px;
                min-height: 44px;
                flex: 1;
                max-width: 140px;
            }

            .question-actions {
                justify-content: center;
                gap: 10px;
            }

            .btn-small {
                padding: 8px 12px;
                font-size: 12px;
            }

            .question-palette {
                padding: 16px;
                margin-top: 15px;
                border-radius: 10px;
            }

            .palette-title {
                font-size: 14px;
                margin-bottom: 12px;
            }

            .palette-grid {
                grid-template-columns: repeat(5, 1fr);
                gap: 8px;
                justify-items: center;
            }

            .palette-item {
                width: 48px;
                height: 48px;
                font-size: 13px;
                border-radius: 8px;
                font-weight: 600;
            }

            .palette-legend {
                gap: 12px;
                margin-top: 10px;
                justify-content: space-around;
                flex-wrap: nowrap;
            }

            .legend-item {
                font-size: 11px;
                gap: 4px;
                white-space: nowrap;
                flex-shrink: 0;
            }

            .legend-color {
                width: 14px;
                height: 14px;
            }

            /* Results Mobile Styles */
            .results-header {
                padding: 30px 20px;
            }

            .results-title {
                font-size: 22px;
                margin-bottom: 12px;
            }

            .results-score {
                font-size: 42px;
                margin: 20px 0;
            }

            .results-percentage {
                font-size: 16px;
                padding: 6px 16px;
                margin-bottom: 16px;
            }

            .results-progress {
                max-width: 100%;
            }

            .progress-label {
                font-size: 12px;
                margin-bottom: 6px;
            }

            .progress-container {
                height: 6px;
            }

            .results-stats {
                grid-template-columns: 1fr 1fr;
                gap: 12px;
                padding: 20px;
            }

            .stat-item {
                padding: 16px 12px;
            }

            .stat-number {
                font-size: 24px;
                margin-bottom: 6px;
            }

            .stat-label {
                font-size: 12px;
            }

            .results-review {
                padding: 15px;
            }

            .review-title {
                font-size: 18px;
                margin-bottom: 15px;
            }

            .question-review {
                padding: 15px;
                margin-bottom: 20px;
            }

            .review-question-text {
                font-size: 15px;
                line-height: 1.4;
            }

            .review-option {
                padding: 6px 10px;
                font-size: 13px;
                margin: 3px 0;
            }

            .review-explanation {
                padding: 12px;
                font-size: 13px;
                line-height: 1.4;
            }

            .results-actions {
                padding: 20px;
                flex-direction: column;
                gap: 12px;
            }

            .btn-retake,
            .btn-home {
                width: 100%;
                max-width: 280px;
                margin: 0 auto;
                padding: 14px 24px;
                font-size: 15px;
                min-height: 44px;
            }
        }

        @media (max-width: 480px) {
            .exam-container {
                margin: 10px auto;
                padding: 0 10px;
            }

            .exam-header {
                padding: 20px 15px;
                margin-bottom: 15px;
            }

            .exam-title {
                font-size: 22px;
                margin-bottom: 5px;
                line-height: 1.2;
            }

            .exam-subtitle {
                font-size: 14px;
                margin-bottom: 15px;
                line-height: 1.4;
            }

            .exam-info-bar {
                gap: 6px;
                margin-top: 15px;
                justify-content: space-between;
            }

            .info-item {
                padding: 10px 6px;
                max-width: none;
                gap: 4px;
                flex: 1;
                flex-direction: column;
                align-items: center;
            }

            .info-item i {
                font-size: 14px;
            }

            .info-item span {
                font-size: 11px;
                text-align: center;
                font-weight: 600;
                line-height: 1.2;
            }

            .exam-nav {
                padding: 10px 15px;
                gap: 8px;
            }

            .question-number {
                font-size: 12px;
            }

            .progress-bar {
                width: 80px;
                height: 5px;
            }

            .timer {
                padding: 5px 10px;
                border-radius: 15px;
            }

            .timer i {
                font-size: 11px;
            }

            .timer span {
                font-size: 12px;
            }

            .question-content {
                padding: 15px;
            }

            .question-text {
                font-size: 15px;
            }

            .option-item label {
                font-size: 14px;
            }

            .question-palette {
                padding: 12px;
                margin-top: 12px;
            }

            .palette-title {
                font-size: 13px;
                margin-bottom: 10px;
            }

            .palette-grid {
                grid-template-columns: repeat(5, 1fr);
                gap: 6px;
            }

            .palette-item {
                width: 42px;
                height: 42px;
                font-size: 12px;
                border-radius: 6px;
                font-weight: 600;
            }

            .results-header {
                padding: 25px 15px;
            }

            .results-title {
                font-size: 20px;
                margin-bottom: 10px;
            }

            .results-score {
                font-size: 38px;
                margin: 18px 0;
            }

            .results-percentage {
                font-size: 15px;
                padding: 5px 14px;
                margin-bottom: 14px;
            }

            .progress-label {
                font-size: 11px;
                margin-bottom: 5px;
            }

            .progress-container {
                height: 5px;
            }

            .results-stats {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
                padding: 16px;
            }

            .stat-item {
                padding: 14px 10px;
            }

            .stat-number {
                font-size: 22px;
                margin-bottom: 5px;
            }

            .stat-label {
                font-size: 11px;
            }

            .question-review {
                padding: 12px;
            }

            .review-question-text {
                font-size: 14px;
            }

            .review-option {
                font-size: 12px;
            }

            .review-explanation {
                font-size: 12px;
            }

            .results-actions {
                padding: 16px;
                gap: 10px;
            }

            .btn-retake,
            .btn-home {
                padding: 12px 20px;
                font-size: 14px;
                min-height: 42px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="images/logo/logo.png" alt="DRX Pharma Academy" style="height: 40px;">
                </div>
                <nav class="nav">
                    <a href="index.html">Home</a>
                    <a href="archive-study-materials.html">Study Materials</a>
                    <a href="archive-exams.html">Exams</a>
                    <a href="archive-notifications.html">Notifications</a>
                    <a href="about.html">About</a>
                </nav>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars" id="menuIcon"></i>
                </button>
                <a href="#" class="whatsapp-btn">
                    <i class="fab fa-whatsapp"></i> Join WhatsApp
                </a>
            </div>
            <nav class="nav-mobile" id="navMobile">
                <a href="index.html">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="archive-study-materials.html">
                    <i class="fas fa-book"></i> Study Materials
                </a>
                <a href="archive-exams.html">
                    <i class="fas fa-file-alt"></i> Exams
                </a>
                <a href="archive-notifications.html">
                    <i class="fas fa-bell"></i> Notifications
                </a>
                <a href="about.html">
                    <i class="fas fa-info-circle"></i> About
                </a>
                <div class="whatsapp-container">
                    <a href="#" class="whatsapp-btn">
                        <i class="fab fa-whatsapp"></i> Join WhatsApp Group
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Breadcrumb Navigation -->
    <section class="breadcrumb-section">
        <div class="container">
            <div class="breadcrumb-wrapper">
                <nav class="breadcrumb">
                    <a href="index.html" class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-arrow"></i>
                    <a href="archive-exams.html" class="breadcrumb-item">
                        <i class="fas fa-file-alt"></i>
                        <span>Exams</span>
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-arrow"></i>
                    <a href="exam-category.html" class="breadcrumb-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span>GPAT</span>
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-arrow"></i>
                    <span class="breadcrumb-current">
                        <i class="fas fa-edit"></i>
                        <span>Mock Test</span>
                    </span>
                </nav>
                <div class="breadcrumb-actions">
                    <button class="back-btn" onclick="window.history.back()">
                        <i class="fas fa-arrow-left"></i>
                        Back
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Exam Container -->
    <div class="exam-container">
        <!-- Exam Header -->
        <div class="exam-header">
            <div class="exam-header-content">
                <h1 class="exam-title">GPAT Mock Test Series - 2024</h1>
                <p class="exam-subtitle">Graduate Pharmacy Aptitude Test Practice Exam</p>
                <div class="exam-info-bar">
                    <div class="info-item">
                        <i class="fas fa-question-circle"></i>
                        <span>25 Questions</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <span>45 Minutes</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-star"></i>
                        <span>25 Marks</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exam Window -->
        <div class="exam-window">
            <!-- Exam Navigation -->
            <div class="exam-nav">
                <div class="question-progress">
                    <span class="question-number">Question 1 of 125</span>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
                <div class="timer">
                    <i class="fas fa-clock"></i>
                    <span>2:59:45</span>
                </div>
            </div>

            <!-- Question Content -->
            <div class="question-content">
                <div class="question-text">
                    <strong>Question 1:</strong> Which of the following is the primary mechanism of action of aspirin as an anti-inflammatory agent?
                </div>

                <ul class="options-list">
                    <li class="option-item">
                        <input type="radio" name="q1" id="q1a" value="a">
                        <label for="q1a">Inhibition of cyclooxygenase (COX) enzymes</label>
                    </li>
                    <li class="option-item">
                        <input type="radio" name="q1" id="q1b" value="b">
                        <label for="q1b">Blockade of histamine H1 receptors</label>
                    </li>
                    <li class="option-item">
                        <input type="radio" name="q1" id="q1c" value="c">
                        <label for="q1c">Inhibition of phospholipase A2</label>
                    </li>
                    <li class="option-item">
                        <input type="radio" name="q1" id="q1d" value="d">
                        <label for="q1d">Antagonism of leukotriene receptors</label>
                    </li>
                </ul>
            </div>

            <!-- Exam Controls -->
            <div class="exam-controls">
                <div class="nav-buttons">
                    <button class="btn btn-previous" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <button class="btn btn-next">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="question-actions">
                    <button class="btn-small">
                        <i class="fas fa-eraser"></i> Clear Response
                    </button>
                    <button class="btn btn-submit">
                        <i class="fas fa-paper-plane"></i> Submit 
                    </button>
                </div>
            </div>
        </div>

        <!-- Question Palette -->
        <div class="question-palette">
            <div class="palette-title">Question Palette</div>
            <div class="palette-grid">
                <!-- Questions will be generated dynamically -->
            </div>
            <div class="palette-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #667eea;"></div>
                    <span>Current</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #10b981;"></div>
                    <span>Answered</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e2e8f0;"></div>
                    <span>Not Attempted</span>
                </div>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container" id="resultsContainer">
            <div class="results-header">
                <div class="results-header-content">
                    <h2 class="results-title">Exam Completed!</h2>
                    <div class="results-score" id="resultsScore">0/25</div>
                    <div class="results-percentage" id="resultsPercentage">0%</div>
                    <div class="results-progress">
                        <div class="progress-label">
                            <span>Correct</span>
                            <span>Incorrect</span>
                        </div>
                        <div class="progress-container">
                            <div class="progress-fill-correct" id="progressCorrect" style="width: 0%"></div>
                            <div class="progress-fill-incorrect" id="progressIncorrect" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="results-stats">
                <div class="stat-item correct">
                    <div class="stat-number" id="correctAnswers">0</div>
                    <div class="stat-label">Correct</div>
                </div>
                <div class="stat-item incorrect">
                    <div class="stat-number" id="incorrectAnswers">0</div>
                    <div class="stat-label">Incorrect</div>
                </div>
                <div class="stat-item unanswered">
                    <div class="stat-number" id="unansweredQuestions">0</div>
                    <div class="stat-label">Unanswered</div>
                </div>
                <div class="stat-item time">
                    <div class="stat-number" id="timeSpent">--:--</div>
                    <div class="stat-label">Time Spent</div>
                </div>
            </div>

            <div class="results-review">
                <h3 class="review-title">Answer Review</h3>
                <div id="reviewContent">
                    <!-- Question reviews will be generated here -->
                </div>
            </div>

            <div class="results-actions">
                <button class="btn-retake" onclick="retakeExam()">
                    <i class="fas fa-redo"></i> Retake Exam
                </button>
                <a href="archive-exams.html" class="btn-home">
                    <i class="fas fa-home"></i> Back to Exams
                </a>
            </div>
        </div>
    </div>

    <!-- Footer Section -->
    <footer class="footer">
        <div class="container">
            <div class="footer-main">
                <div class="footer-logo">
                    <h2><span style="font-size: 1.5em; font-weight: 700;">DRX</span> <span style="font-size: 0.6em; font-weight: 400;">Pharma Academy</span></h2>
                    <p class="footer-tagline">Your pathway to success in competitive exams</p>
                </div>
                
                <div class="footer-groups">
                    <h3 class="group-title">Join Our Study Groups</h3>
                    <div class="group-buttons">
                        <a href="#" class="group-btn whatsapp-btn">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp Group
                        </a>
                        <a href="#" class="group-btn telegram-btn">
                            <i class="fab fa-telegram"></i>
                            Telegram Channel
                        </a>
                    </div>
                </div>
                
                <div class="footer-social">
                    <h3 class="social-title">Follow Us</h3>
                    <div class="social-links">
                        <a href="#" class="social-link social-facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link social-instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link social-youtube">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link social-linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-links">
                <div class="footer-links-grid">
                    <div class="footer-section">
                        <h3>Study Materials</h3>
                        <ul>
                            <li><a href="#">Pharmacology</a></li>
                            <li><a href="#">Pharmaceutical Chemistry</a></li>
                            <li><a href="#">Pharmacognosy</a></li>
                            <li><a href="#">Pharmaceutics</a></li>
                            <li><a href="#">Clinical Pharmacy</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Exam Prep</h3>
                        <ul>
                            <li><a href="#">GPAT Preparation</a></li>
                            <li><a href="#">NIPER JEE</a></li>
                            <li><a href="#">Drug Inspector</a></li>
                            <li><a href="#">Previous Papers</a></li>
                            <li><a href="#">Mock Tests</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Resources</h3>
                        <ul>
                            <li><a href="#">Study Notes</a></li>
                            <li><a href="#">Question Banks</a></li>
                            <li><a href="#">Video Lectures</a></li>
                            <li><a href="#">E-books</a></li>
                            <li><a href="#">Reference Materials</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Support</h3>
                        <ul>
                            <li><a href="contact.html">Contact Us</a></li>
                            <li><a href="about.html">About Us</a></li>
                            <li><a href="terms-conditions.html">Terms & Conditions</a></li>
                            <li><a href="privacy-policy.html">Privacy Policy</a></li>
                            <li><a href="sitemap.xml">Sitemap</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2024 DRX Pharma Academy. All rights reserved. | Designed for pharmacy competitive exam aspirants</p>
        </div>
    </footer>

    <script src="script.js"></script>
    
    <!-- Exam Functionality Script -->
    <script>
        // Exam data with 25 real GPAT questions
        const examData = {
            title: "GPAT Mock Test Series - 2024",
            totalQuestions: 25,
            timeLimit: 45, // 45 minutes
            questions: [
                {
                    id: 1,
                    text: "Which of the following is the primary mechanism of action of aspirin as an anti-inflammatory agent?",
                    options: [
                        "Inhibition of cyclooxygenase (COX) enzymes",
                        "Blockade of histamine H1 receptors", 
                        "Inhibition of phospholipase A2",
                        "Antagonism of leukotriene receptors"
                    ],
                    correct: 0,
                    explanation: "Aspirin irreversibly inhibits cyclooxygenase (COX) enzymes, preventing the conversion of arachidonic acid to prostaglandins, which are key mediators of inflammation."
                },
                {
                    id: 2,
                    text: "What is the bioavailability of a drug administered intravenously?",
                    options: [
                        "50%",
                        "75%",
                        "90%",
                        "100%"
                    ],
                    correct: 3,
                    explanation: "Intravenous administration bypasses all absorption barriers, resulting in 100% bioavailability as the entire dose reaches systemic circulation."
                },
                {
                    id: 3,
                    text: "Which pharmacokinetic parameter represents the time required for the plasma concentration to reduce by half?",
                    options: [
                        "Clearance",
                        "Volume of distribution",
                        "Half-life",
                        "Bioavailability"
                    ],
                    correct: 2,
                    explanation: "Half-life (t½) is the time required for the plasma concentration of a drug to decrease by 50% during the elimination phase."
                },
                {
                    id: 4,
                    text: "What is the primary site of drug metabolism in the human body?",
                    options: [
                        "Kidneys",
                        "Liver",
                        "Lungs",
                        "Small intestine"
                    ],
                    correct: 1,
                    explanation: "The liver is the primary site of drug metabolism, containing the highest concentration of drug-metabolizing enzymes, particularly cytochrome P450 enzymes."
                },
                {
                    id: 5,
                    text: "Which enzyme system is primarily responsible for Phase I drug metabolism?",
                    options: [
                        "UDP-glucuronosyltransferase",
                        "Cytochrome P450",
                        "Sulfotransferase",
                        "N-acetyltransferase"
                    ],
                    correct: 1,
                    explanation: "Cytochrome P450 enzymes are the major enzyme system responsible for Phase I metabolism, which includes oxidation, reduction, and hydrolysis reactions."
                },
                {
                    id: 6,
                    text: "Which of the following is a selective COX-2 inhibitor?",
                    options: [
                        "Ibuprofen",
                        "Aspirin",
                        "Celecoxib",
                        "Naproxen"
                    ],
                    correct: 2,
                    explanation: "Celecoxib is a selective COX-2 inhibitor, designed to reduce gastrointestinal side effects associated with non-selective NSAIDs."
                },
                {
                    id: 7,
                    text: "The Henderson-Hasselbalch equation is used to calculate:",
                    options: [
                        "Drug clearance",
                        "pH and ionization of drugs",
                        "Bioequivalence",
                        "Volume of distribution"
                    ],
                    correct: 1,
                    explanation: "The Henderson-Hasselbalch equation relates pH, pKa, and the ionization state of weak acids and bases, crucial for understanding drug absorption and distribution."
                },
                {
                    id: 8,
                    text: "Which type of diabetes is characterized by insulin resistance?",
                    options: [
                        "Type 1 diabetes",
                        "Type 2 diabetes",
                        "MODY diabetes",
                        "Secondary diabetes"
                    ],
                    correct: 1,
                    explanation: "Type 2 diabetes is characterized by insulin resistance, where cells become less responsive to insulin, often combined with relative insulin deficiency."
                },
                {
                    id: 9,
                    text: "The drug of choice for treating severe anaphylactic reactions is:",
                    options: [
                        "Diphenhydramine",
                        "Epinephrine",
                        "Prednisolone",
                        "Albuterol"
                    ],
                    correct: 1,
                    explanation: "Epinephrine is the first-line treatment for anaphylaxis due to its rapid onset and ability to counteract severe allergic reactions through multiple mechanisms."
                },
                {
                    id: 10,
                    text: "Which of the following alkaloids is derived from Cinchona bark?",
                    options: [
                        "Morphine",
                        "Quinine",
                        "Atropine",
                        "Caffeine"
                    ],
                    correct: 1,
                    explanation: "Quinine is the major alkaloid derived from Cinchona bark and has been historically used as an antimalarial agent."
                },
                {
                    id: 11,
                    text: "The mechanism of action of warfarin involves:",
                    options: [
                        "Direct thrombin inhibition",
                        "Vitamin K antagonism",
                        "Factor Xa inhibition",
                        "Platelet aggregation inhibition"
                    ],
                    correct: 1,
                    explanation: "Warfarin acts as a vitamin K antagonist, preventing the synthesis of vitamin K-dependent clotting factors (II, VII, IX, X)."
                },
                {
                    id: 12,
                    text: "Which excipient is commonly used as a disintegrant in tablets?",
                    options: [
                        "Magnesium stearate",
                        "Croscarmellose sodium",
                        "Polyethylene glycol",
                        "Talc"
                    ],
                    correct: 1,
                    explanation: "Croscarmellose sodium is a superdisintegrant that rapidly swells in contact with water, facilitating tablet disintegration and drug dissolution."
                },
                {
                    id: 13,
                    text: "The Biopharmaceutics Classification System (BCS) classifies drugs based on:",
                    options: [
                        "Solubility and permeability",
                        "Stability and toxicity",
                        "Efficacy and safety",
                        "Molecular weight and pKa"
                    ],
                    correct: 0,
                    explanation: "BCS classifies drugs into four classes based on their aqueous solubility and intestinal permeability, which are fundamental to oral drug absorption."
                },
                {
                    id: 14,
                    text: "Which analytical technique is most commonly used for drug identification?",
                    options: [
                        "UV-Visible spectroscopy",
                        "High Performance Liquid Chromatography (HPLC)",
                        "Infrared (IR) spectroscopy",
                        "Thin Layer Chromatography (TLC)"
                    ],
                    correct: 2,
                    explanation: "IR spectroscopy is widely used for drug identification as it provides characteristic fingerprint patterns unique to specific molecular structures."
                },
                {
                    id: 15,
                    text: "The first-pass effect is most significant for drugs administered via:",
                    options: [
                        "Intravenous route",
                        "Sublingual route",
                        "Oral route",
                        "Transdermal route"
                    ],
                    correct: 2,
                    explanation: "The first-pass effect is most significant for orally administered drugs, as they must pass through the liver before reaching systemic circulation."
                },
                {
                    id: 16,
                    text: "Which plant is the source of the cardiac glycoside digoxin?",
                    options: [
                        "Digitalis purpurea",
                        "Catharanthus roseus",
                        "Atropa belladonna",
                        "Papaver somniferum"
                    ],
                    correct: 0,
                    explanation: "Digitalis purpurea (foxglove) is the plant source of cardiac glycosides including digoxin, used in treating heart failure and atrial fibrillation."
                },
                {
                    id: 17,
                    text: "The therapeutic drug monitoring is most important for:",
                    options: [
                        "Drugs with wide therapeutic index",
                        "Drugs with narrow therapeutic index",
                        "Over-the-counter medications",
                        "Topical preparations"
                    ],
                    correct: 1,
                    explanation: "Therapeutic drug monitoring is crucial for drugs with narrow therapeutic index, where small changes in concentration can lead to toxicity or therapeutic failure."
                },
                {
                    id: 18,
                    text: "Which type of drug interaction occurs when two drugs compete for the same receptor?",
                    options: [
                        "Pharmacokinetic interaction",
                        "Pharmaceutical interaction",
                        "Pharmacodynamic interaction",
                        "Chemical interaction"
                    ],
                    correct: 2,
                    explanation: "Pharmacodynamic interactions occur at the receptor level, including competition for the same receptor, resulting in additive, synergistic, or antagonistic effects."
                },
                {
                    id: 19,
                    text: "The antidote for acetaminophen (paracetamol) poisoning is:",
                    options: [
                        "Flumazenil",
                        "N-acetylcysteine",
                        "Naloxone",
                        "Protamine sulfate"
                    ],
                    correct: 1,
                    explanation: "N-acetylcysteine replenishes glutathione stores and prevents hepatotoxicity caused by the toxic metabolite NAPQI in acetaminophen overdose."
                },
                {
                    id: 20,
                    text: "Which preservative is commonly used in ophthalmic preparations?",
                    options: [
                        "Benzalkonium chloride",
                        "Sodium benzoate",
                        "Methyl paraben",
                        "Phenol"
                    ],
                    correct: 0,
                    explanation: "Benzalkonium chloride is the most commonly used preservative in ophthalmic preparations due to its broad antimicrobial spectrum and compatibility with eye tissues."
                },
                {
                    id: 21,
                    text: "The bioequivalence study compares:",
                    options: [
                        "Different drugs for same indication",
                        "Same drug in different formulations",
                        "Drug and placebo",
                        "Different doses of same drug"
                    ],
                    correct: 1,
                    explanation: "Bioequivalence studies compare the rate and extent of absorption of the same drug from different formulations to ensure therapeutic equivalence."
                },
                {
                    id: 22,
                    text: "Which factor does NOT affect drug absorption from the GI tract?",
                    options: [
                        "Gastric pH",
                        "Intestinal motility",
                        "Blood-brain barrier",
                        "Drug solubility"
                    ],
                    correct: 2,
                    explanation: "The blood-brain barrier affects drug distribution to the brain, not absorption from the GI tract. Gastric pH, motility, and solubility all affect GI absorption."
                },
                {
                    id: 23,
                    text: "The process of freeze-drying is also known as:",
                    options: [
                        "Sublimation",
                        "Lyophilization",
                        "Evaporation",
                        "Crystallization"
                    ],
                    correct: 1,
                    explanation: "Lyophilization (freeze-drying) involves freezing a solution and then removing water by sublimation under vacuum, preserving the structure of heat-sensitive materials."
                },
                {
                    id: 24,
                    text: "Which quality control test is used to determine tablet hardness?",
                    options: [
                        "Friability test",
                        "Disintegration test",
                        "Compression test",
                        "Dissolution test"
                    ],
                    correct: 2,
                    explanation: "Compression test (tablet hardness test) measures the force required to break a tablet, indicating its mechanical strength and potential for handling during manufacturing and packaging."
                },
                {
                    id: 25,
                    text: "The term 'polymorphism' in pharmaceutical sciences refers to:",
                    options: [
                        "Multiple therapeutic uses of a drug",
                        "Different crystal forms of the same compound",
                        "Various routes of administration",
                        "Different metabolic pathways"
                    ],
                    correct: 1,
                    explanation: "Polymorphism refers to the ability of a compound to exist in different crystal forms, which can affect solubility, stability, and bioavailability of drugs."
                }
            ]
        };

        // Exam state
        let currentQuestion = 0;
        let answers = {};
        let timeRemaining = examData.timeLimit * 60; // Convert to seconds
        let timerInterval;
        let examStartTime;
        let examCompleted = false;

        // Initialize exam
        function initializeExam() {
            examStartTime = new Date();
            displayQuestion(currentQuestion);
            generateQuestionPalette();
            startTimer();
            updateProgress();
        }

        // Display current question
        function displayQuestion(index) {
            const question = examData.questions[index];
            if (!question) return;

            document.querySelector('.question-number').textContent = `Question ${index + 1} of ${examData.totalQuestions}`;
            document.querySelector('.question-text').innerHTML = question.text;

            const optionsList = document.querySelector('.options-list');
            optionsList.innerHTML = '';

            question.options.forEach((option, i) => {
                const li = document.createElement('li');
                li.className = 'option-item';
                li.innerHTML = `
                    <input type="radio" name="q${question.id}" id="q${question.id}${String.fromCharCode(97 + i)}" value="${i}">
                    <label for="q${question.id}${String.fromCharCode(97 + i)}">${option}</label>
                `;
                optionsList.appendChild(li);
            });

            // Restore previous answer if exists
            if (answers[question.id] !== undefined) {
                const radio = document.querySelector(`input[name="q${question.id}"][value="${answers[question.id]}"]`);
                if (radio) radio.checked = true;
            }

            // Update navigation buttons
            document.querySelector('.btn-previous').disabled = index === 0;
            const nextBtn = document.querySelector('.btn-next');
            if (index === examData.questions.length - 1) {
                nextBtn.style.display = 'none';
                document.querySelector('.btn-submit').style.display = 'inline-flex';
            } else {
                nextBtn.style.display = 'inline-flex';
                document.querySelector('.btn-submit').style.display = 'none';
            }

            // Update palette
            updateQuestionPalette();
        }

        // Generate question palette
        function generateQuestionPalette() {
            const grid = document.querySelector('.palette-grid');
            grid.innerHTML = '';

            for (let i = 0; i < examData.totalQuestions; i++) {
                const div = document.createElement('div');
                div.className = 'palette-item';
                div.textContent = i + 1;
                div.onclick = () => goToQuestion(i);
                grid.appendChild(div);
            }
        }

        // Update question palette styling
        function updateQuestionPalette() {
            document.querySelectorAll('.palette-item').forEach((item, index) => {
                item.className = 'palette-item';
                
                if (index === currentQuestion) {
                    item.classList.add('current');
                } else if (answers[index + 1] !== undefined) {
                    item.classList.add('answered');
                }
            });
        }

        // Go to specific question
        function goToQuestion(index) {
            if (index >= 0 && index < examData.questions.length) {
                saveCurrentAnswer();
                currentQuestion = index;
                displayQuestion(currentQuestion);
                updateProgress();
            }
        }

        // Save current answer
        function saveCurrentAnswer() {
            const question = examData.questions[currentQuestion];
            const selected = document.querySelector(`input[name="q${question.id}"]:checked`);
            if (selected) {
                answers[question.id] = parseInt(selected.value);
            }
        }

        // Update progress bar
        function updateProgress() {
            const progress = ((currentQuestion + 1) / examData.totalQuestions) * 100;
            document.querySelector('.progress-fill').style.width = progress + '%';
        }

        // Start timer
        function startTimer() {
            timerInterval = setInterval(() => {
                timeRemaining--;
                updateTimerDisplay();
                
                if (timeRemaining <= 0) {
                    examCompleted = true;
                    showResults();
                }
            }, 1000);
        }

        // Update timer display
        function updateTimerDisplay() {
            const hours = Math.floor(timeRemaining / 3600);
            const minutes = Math.floor((timeRemaining % 3600) / 60);
            const seconds = timeRemaining % 60;
            
            document.querySelector('.timer span').textContent = 
                `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Navigation functions
        function previousQuestion() {
            if (currentQuestion > 0) {
                saveCurrentAnswer();
                currentQuestion--;
                displayQuestion(currentQuestion);
                updateProgress();
            }
        }

        function nextQuestion() {
            if (currentQuestion < examData.questions.length - 1) {
                saveCurrentAnswer();
                currentQuestion++;
                displayQuestion(currentQuestion);
                updateProgress();
            }
        }


        // Clear response
        function clearResponse() {
            const question = examData.questions[currentQuestion];
            const radios = document.querySelectorAll(`input[name="q${question.id}"]`);
            radios.forEach(radio => radio.checked = false);
            delete answers[question.id];
            updateQuestionPalette();
        }

        // Submit exam
        function submitExam() {
            if (examCompleted) return;
            
            saveCurrentAnswer();
            clearInterval(timerInterval);
            examCompleted = true;
            showResults();
        }

        // Show results
        function showResults() {
            const score = calculateScore();
            const answeredCount = Object.keys(answers).length;
            const unansweredCount = examData.totalQuestions - answeredCount;
            const timeSpent = calculateTimeSpent();
            
            // Calculate correct and incorrect answers
            let correctCount = 0;
            let incorrectCount = 0;
            
            examData.questions.forEach(question => {
                const userAnswer = answers[question.id];
                if (userAnswer !== undefined) {
                    if (userAnswer === question.correct) {
                        correctCount++;
                    } else {
                        incorrectCount++;
                    }
                }
            });

            // Calculate percentage based on total possible marks (25)
            const maxPossibleScore = examData.totalQuestions;
            const percentage = ((score / maxPossibleScore) * 100).toFixed(1);
            
            // Calculate progress percentages
            const correctPercentage = ((correctCount / examData.totalQuestions) * 100).toFixed(1);
            const incorrectPercentage = ((incorrectCount / examData.totalQuestions) * 100).toFixed(1);

            // Hide exam interface
            document.querySelector('.exam-window').style.display = 'none';
            document.querySelector('.question-palette').style.display = 'none';

            // Show results
            document.getElementById('resultsContainer').style.display = 'block';
            document.getElementById('resultsScore').textContent = `${score}/${maxPossibleScore}`;
            document.getElementById('resultsPercentage').textContent = `${percentage}%`;
            document.getElementById('correctAnswers').textContent = correctCount;
            document.getElementById('incorrectAnswers').textContent = incorrectCount;
            document.getElementById('unansweredQuestions').textContent = unansweredCount;
            document.getElementById('timeSpent').textContent = timeSpent;

            // Animate progress bars
            setTimeout(() => {
                document.getElementById('progressCorrect').style.width = `${correctPercentage}%`;
                document.getElementById('progressIncorrect').style.width = `${incorrectPercentage}%`;
            }, 500);

            // Generate detailed review
            generateReview();

            // Scroll to results
            document.getElementById('resultsContainer').scrollIntoView({ behavior: 'smooth' });
        }

        // Generate detailed review
        function generateReview() {
            const reviewContent = document.getElementById('reviewContent');
            reviewContent.innerHTML = '';

            examData.questions.forEach((question, index) => {
                const userAnswer = answers[question.id];
                const isCorrect = userAnswer === question.correct;
                const isUnanswered = userAnswer === undefined;

                const reviewDiv = document.createElement('div');
                reviewDiv.className = `question-review ${isUnanswered ? 'unanswered' : isCorrect ? 'correct' : 'incorrect'}`;

                let optionsHtml = '';
                question.options.forEach((option, optionIndex) => {
                    let optionClass = '';
                    if (optionIndex === question.correct) {
                        optionClass = 'correct-answer';
                    }
                    if (userAnswer === optionIndex && !isCorrect) {
                        optionClass = 'wrong-answer';
                    }
                    if (userAnswer === optionIndex && isCorrect) {
                        optionClass = 'user-answer correct-answer';
                    }

                    optionsHtml += `<div class="review-option ${optionClass}">${String.fromCharCode(65 + optionIndex)}. ${option}</div>`;
                });

                reviewDiv.innerHTML = `
                    <div class="review-question-text">
                        <strong>Question ${index + 1}:</strong> ${question.text}
                    </div>
                    <div class="review-options">
                        ${optionsHtml}
                    </div>
                    <div class="review-explanation">
                        <strong>Explanation:</strong> ${question.explanation}
                    </div>
                `;

                reviewContent.appendChild(reviewDiv);
            });
        }

        // Calculate time spent
        function calculateTimeSpent() {
            if (!examStartTime) return '--:--';
            const endTime = new Date();
            const timeDiff = endTime - examStartTime;
            const minutes = Math.floor(timeDiff / 60000);
            const seconds = Math.floor((timeDiff % 60000) / 1000);
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        // Retake exam
        function retakeExam() {
            if (confirm('Are you sure you want to retake the exam? All progress will be lost.')) {
                location.reload();
            }
        }

        // Calculate score with negative marking
        function calculateScore() {
            let score = 0;
            examData.questions.forEach(question => {
                const userAnswer = answers[question.id];
                if (userAnswer !== undefined) {
                    if (userAnswer === question.correct) {
                        score += 1; // +1 for correct answer
                    } else {
                        score -= 0.25; // -0.25 for incorrect answer
                    }
                }
                // No marks for skipped questions (userAnswer === undefined)
            });
            return Math.round(score * 100) / 100; // Round to 2 decimal places
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize exam
            initializeExam();

            // Navigation buttons
            document.querySelector('.btn-previous').onclick = previousQuestion;
            document.querySelector('.btn-next').onclick = nextQuestion;
            document.querySelector('.btn-submit').onclick = submitExam;

            // Action buttons
            document.querySelector('.btn-small').onclick = clearResponse;

            // Auto-save answers when radio buttons change
            document.addEventListener('change', function(e) {
                if (e.target.type === 'radio') {
                    saveCurrentAnswer();
                    updateQuestionPalette();
                }
            });

            // Prevent accidental page refresh
            window.addEventListener('beforeunload', function(e) {
                e.preventDefault();
                e.returnValue = '';
            });
        });
    </script>
</body>
</html>