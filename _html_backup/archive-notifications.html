<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - DRX Pharma Academy</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Use exact homepage notification styles */
        .notifications-list {
            /* Override to use homepage styles */
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="images/logo/logo.png" alt="DRX Pharma Academy" style="height: 40px;">
                </div>
                <nav class="nav">
                    <a href="index.html">Home</a>
                    <a href="archive-study-materials.html">Study Materials</a>
                    <a href="archive-exams.html">Exams</a>
                    <a href="archive-notifications.html" class="active">Notifications</a>
                    <a href="about.html">About</a>
                </nav>
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars" id="menuIcon"></i>
                </button>
                <a href="#" class="whatsapp-btn">
                    <i class="fab fa-whatsapp"></i> Join WhatsApp
                </a>
            </div>
            <nav class="nav-mobile" id="navMobile">
                <a href="index.html">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="archive-study-materials.html">
                    <i class="fas fa-book"></i> Study Materials
                </a>
                <a href="archive-exams.html">
                    <i class="fas fa-file-alt"></i> Exams
                </a>
                <a href="archive-notifications.html" class="active">
                    <i class="fas fa-bell"></i> Notifications
                </a>
                <a href="about.html">
                    <i class="fas fa-info-circle"></i> About
                </a>
                <div class="whatsapp-container">
                    <a href="#" class="whatsapp-btn">
                        <i class="fab fa-whatsapp"></i> Join WhatsApp Group
                    </a>
                </div>
            </nav>
            </div>
        </div>
    </header>

    <!-- Page Header Section -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    Latest Notifications
                </h1>
                <p class="page-subtitle">Stay updated with the latest exam notifications, announcements, and important updates</p>
            </div>
        </div>
    </section>

    <!-- Banner Ad 1 - After Title Section -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner3.jpeg" alt="Advertisement">
    </div>


    <!-- Notifications List Section -->
    <section class="notifications-list-section">
        <div class="container">
            <div class="notifications-count">
                <span id="notificationsCount">Showing all notifications</span>
            </div>
            
            <div class="notifications-grid" id="notificationsList">
                <a href="single-notification.html" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">173/2025 - Driver Gr.II (LDV) Driver Cum Office Attendant (LDV) in Various Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 20, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.html" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">171/2025 - Driver Gr.II (HDV) (Ex-servicemen only) in NCC/Sainik Welfare Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 18, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.html" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">169/2025 - Assistant Pharmacist in Health Services Department Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 15, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.html" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">167/2025 - Laboratory Assistant Grade II in Various Departments Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 12, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.html" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">165/2025 - Staff Nurse Grade II in Health Services Department Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 10, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.html" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">163/2025 - Pharmacist Grade II in Ayurveda Medical Education Department Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 8, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>
            </div>

            <!-- Pagination Section -->
            <div class="pagination-section">
                <div class="pagination">
                    <a href="#" class="pagination-btn prev-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                    <div class="pagination-numbers" id="paginationNumbers">
                        <a href="#" class="pagination-number active" data-page="1">1</a>
                        <a href="#" class="pagination-number" data-page="2">2</a>
                        <a href="#" class="pagination-number" data-page="3">3</a>
                        <a href="#" class="pagination-number" data-page="4">4</a>
                        <a href="#" class="pagination-number" data-page="5">5</a>
                    </div>
                    <a href="#" class="pagination-btn next-btn" id="nextBtn">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="pagination-info">
                    <span id="paginationInfo">Showing 1-6 of 25 notifications</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Banner Ad 2 - After Notifications List -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner2.jpeg" alt="Advertisement">
    </div>

    <!-- Footer Section -->
    <footer class="footer">
        <div class="container">
            <div class="footer-main">
                <div class="footer-logo">
                    <h2><span style="font-size: 1.5em; font-weight: 700;">DRX</span> <span style="font-size: 0.6em; font-weight: 400;">Pharma Academy</span></h2>
                    <p class="footer-tagline">Your pathway to success in competitive exams</p>
                </div>
                
                <div class="footer-groups">
                    <h3 class="group-title">Join Our Study Groups</h3>
                    <div class="group-buttons">
                        <a href="#" class="group-btn whatsapp-btn">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp Group
                        </a>
                        <a href="#" class="group-btn telegram-btn">
                            <i class="fab fa-telegram"></i>
                            Telegram Channel
                        </a>
                    </div>
                </div>
                
                <div class="footer-social">
                    <h3 class="social-title">Follow Us</h3>
                    <div class="social-links">
                        <a href="#" class="social-link social-facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link social-instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link social-youtube">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link social-linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-links">
                <div class="footer-links-grid">
                    <div class="footer-section">
                        <h3>Study Materials</h3>
                        <ul>
                            <li><a href="#">Pharmacology</a></li>
                            <li><a href="#">Pharmaceutical Chemistry</a></li>
                            <li><a href="#">Pharmacognosy</a></li>
                            <li><a href="#">Pharmaceutics</a></li>
                            <li><a href="#">Clinical Pharmacy</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Exam Prep</h3>
                        <ul>
                            <li><a href="#">GPAT Preparation</a></li>
                            <li><a href="#">NIPER JEE</a></li>
                            <li><a href="#">Drug Inspector</a></li>
                            <li><a href="#">Previous Papers</a></li>
                            <li><a href="#">Mock Tests</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Resources</h3>
                        <ul>
                            <li><a href="#">Study Notes</a></li>
                            <li><a href="#">Question Banks</a></li>
                            <li><a href="#">Video Lectures</a></li>
                            <li><a href="#">E-books</a></li>
                            <li><a href="#">Reference Materials</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Support</h3>
                        <ul>
                            <li><a href="contact.html">Contact Us</a></li>
                            <li><a href="about.html">About Us</a></li>
                            <li><a href="terms-conditions.html">Terms & Conditions</a></li>
                            <li><a href="privacy-policy.html">Privacy Policy</a></li>
                            <li><a href="sitemap.xml">Sitemap</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2024 DRX Pharma Academy. All rights reserved. | Designed for pharmacy competitive exam aspirants</p>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Initialize notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notificationsCount = document.getElementById('notificationsCount');
            const allNotifications = document.querySelectorAll('.notification-card');
            
            // Set initial count
            notificationsCount.textContent = 'Showing all notifications';

            // Pagination functionality
            const paginationNumbers = document.querySelectorAll('.pagination-number');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const paginationInfo = document.getElementById('paginationInfo');
            let currentPage = 1;
            const totalPages = 5;

            function updatePagination() {
                // Update pagination numbers
                paginationNumbers.forEach(btn => {
                    btn.classList.remove('active');
                    if (parseInt(btn.dataset.page) === currentPage) {
                        btn.classList.add('active');
                    }
                });

                // Update prev/next buttons
                prevBtn.classList.toggle('disabled', currentPage === 1);
                nextBtn.classList.toggle('disabled', currentPage === totalPages);

                // Update pagination info
                const startItem = (currentPage - 1) * 6 + 1;
                const endItem = Math.min(currentPage * 6, 25);
                paginationInfo.textContent = `Showing ${startItem}-${endItem} of 25 notifications`;
            }

            // Pagination number click handlers
            paginationNumbers.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = parseInt(this.dataset.page);
                    updatePagination();
                    // Scroll to top of notifications
                    document.querySelector('.notifications-list-section').scrollIntoView({ behavior: 'smooth' });
                });
            });

            // Previous button click handler
            prevBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    updatePagination();
                    document.querySelector('.notifications-list-section').scrollIntoView({ behavior: 'smooth' });
                }
            });

            // Next button click handler
            nextBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    updatePagination();
                    document.querySelector('.notifications-list-section').scrollIntoView({ behavior: 'smooth' });
                }
            });

            // Initialize pagination
            updatePagination();

            // Clickable card functionality
            const clickableCards = document.querySelectorAll('.notification-card');
            
            clickableCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    // Get the href from data attribute
                    const href = this.dataset.href;
                    
                    if (href) {
                        // You can customize this behavior:
                        // Option 1: Navigate to a specific page
                        // window.location.href = href;
                        
                        // Option 2: Show a modal or detailed view
                        // showNotificationDetails(href);
                        
                        // Option 3: For demo purposes, show an alert
                        const title = this.querySelector('.notification-title').textContent;
                        alert(`Opening notification: ${title}`);
                        
                        // Add visual feedback
                        this.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 150);
                    }
                });
                
                // Add keyboard navigation
                card.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });
                
                // Make cards focusable for accessibility
                card.setAttribute('tabindex', '0');
                card.setAttribute('role', 'button');
                card.setAttribute('aria-label', 'Click to view notification details');
            });

        });
    </script>
</body>
</html>