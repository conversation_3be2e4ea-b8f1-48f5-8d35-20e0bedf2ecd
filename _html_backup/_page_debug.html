<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Debug - HTML vs PHP Links</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #dee2e6;
        }
        td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .page-link {
            display: inline-block;
            padding: 8px 12px;
            margin: 2px 0;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
            text-align: center;
        }
        .html-link {
            background-color: #e74c3c;
            color: white;
        }
        .html-link:hover {
            background-color: #c0392b;
            transform: translateY(-1px);
        }
        .php-link {
            background-color: #27ae60;
            color: white;
        }
        .php-link:hover {
            background-color: #229954;
            transform: translateY(-1px);
        }
        .page-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .type-main {
            background-color: #3498db;
            color: white;
        }
        .type-archive {
            background-color: #f39c12;
            color: white;
        }
        .type-single {
            background-color: #9b59b6;
            color: white;
        }
        .type-category {
            background-color: #1abc9c;
            color: white;
        }
        .page-name {
            font-weight: 600;
            color: #2c3e50;
            margin-top: 5px;
        }
        .stats {
            background-color: #ecf0f1;
            padding: 15px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
        .test-buttons {
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .test-btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 10px;
            background-color: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        .test-btn:hover {
            background-color: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐛 Page Debug Tool</h1>
            <p>Compare HTML and PHP page versions - Click links to test both versions</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th style="width: 50%;">HTML Version</th>
                    <th style="width: 50%;">PHP Version</th>
                </tr>
            </thead>
            <tbody>
                <!-- Main Pages -->
                <tr>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Home Page</div>
                        <a href="index.html" class="page-link html-link" target="_blank">index.html</a>
                    </td>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Home Page</div>
                        <a href="index.php" class="page-link php-link" target="_blank">index.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">About Us</div>
                        <a href="about.html" class="page-link html-link" target="_blank">about.html</a>
                    </td>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">About Us</div>
                        <a href="about.php" class="page-link php-link" target="_blank">about.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Contact Us</div>
                        <a href="contact.html" class="page-link html-link" target="_blank">contact.html</a>
                    </td>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Contact Us</div>
                        <a href="contact.php" class="page-link php-link" target="_blank">contact.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Privacy Policy</div>
                        <a href="privacy-policy.html" class="page-link html-link" target="_blank">privacy-policy.html</a>
                    </td>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Privacy Policy</div>
                        <a href="privacy-policy.php" class="page-link php-link" target="_blank">privacy-policy.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Terms & Conditions</div>
                        <a href="terms-conditions.html" class="page-link html-link" target="_blank">terms-conditions.html</a>
                    </td>
                    <td>
                        <span class="page-type type-main">Main</span>
                        <div class="page-name">Terms & Conditions</div>
                        <a href="terms-conditions.php" class="page-link php-link" target="_blank">terms-conditions.php</a>
                    </td>
                </tr>
                
                <!-- Archive Pages -->
                <tr>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Study Materials Archive</div>
                        <a href="archive-study-materials.html" class="page-link html-link" target="_blank">archive-study-materials.html</a>
                    </td>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Study Materials Archive</div>
                        <a href="archive-study-materials.php" class="page-link php-link" target="_blank">archive-study-materials.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Exams Archive</div>
                        <a href="archive-exams.html" class="page-link html-link" target="_blank">archive-exams.html</a>
                    </td>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Exams Archive</div>
                        <a href="archive-exams.php" class="page-link php-link" target="_blank">archive-exams.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Notifications Archive</div>
                        <a href="archive-notifications.html" class="page-link html-link" target="_blank">archive-notifications.html</a>
                    </td>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Notifications Archive</div>
                        <a href="archive-notifications.php" class="page-link php-link" target="_blank">archive-notifications.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Blog Archive</div>
                        <a href="archive-blog.html" class="page-link html-link" target="_blank">archive-blog.html</a>
                    </td>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Blog Archive</div>
                        <a href="archive-blog.php" class="page-link php-link" target="_blank">archive-blog.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Question Bank Archive</div>
                        <a href="archive-question-bank.html" class="page-link html-link" target="_blank">archive-question-bank.html</a>
                    </td>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Question Bank Archive</div>
                        <a href="archive-question-bank.php" class="page-link php-link" target="_blank">archive-question-bank.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Previous Questions Archive</div>
                        <a href="archive-previous-questions.html" class="page-link html-link" target="_blank">archive-previous-questions.html</a>
                    </td>
                    <td>
                        <span class="page-type type-archive">Archive</span>
                        <div class="page-name">Previous Questions Archive</div>
                        <a href="archive-previous-questions.php" class="page-link php-link" target="_blank">archive-previous-questions.php</a>
                    </td>
                </tr>
                
                <!-- Single Pages -->
                <tr>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Study Material</div>
                        <a href="single-study-material.html" class="page-link html-link" target="_blank">single-study-material.html</a>
                    </td>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Study Material</div>
                        <a href="single-study-material.php" class="page-link php-link" target="_blank">single-study-material.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Exam</div>
                        <a href="single-exam.html" class="page-link html-link" target="_blank">single-exam.html</a>
                    </td>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Exam</div>
                        <a href="single-exam.php" class="page-link php-link" target="_blank">single-exam.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Notification</div>
                        <a href="single-notification.html" class="page-link html-link" target="_blank">single-notification.html</a>
                    </td>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Notification</div>
                        <a href="single-notification.php" class="page-link php-link" target="_blank">single-notification.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Blog Post</div>
                        <a href="single-blog-post.html" class="page-link html-link" target="_blank">single-blog-post.html</a>
                    </td>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Blog Post</div>
                        <a href="single-blog-post.php" class="page-link php-link" target="_blank">single-blog-post.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Previous Question</div>
                        <a href="single-previous-question.html" class="page-link html-link" target="_blank">single-previous-question.html</a>
                    </td>
                    <td>
                        <span class="page-type type-single">Single</span>
                        <div class="page-name">Single Previous Question</div>
                        <a href="single-previous-question.php" class="page-link php-link" target="_blank">single-previous-question.php</a>
                    </td>
                </tr>
                
                <!-- Category Pages -->
                <tr>
                    <td>
                        <span class="page-type type-category">Category</span>
                        <div class="page-name">Question Bank</div>
                        <a href="question-bank.html" class="page-link html-link" target="_blank">question-bank.html</a>
                    </td>
                    <td>
                        <span class="page-type type-category">Category</span>
                        <div class="page-name">Question Bank</div>
                        <a href="question-bank.php" class="page-link php-link" target="_blank">question-bank.php</a>
                    </td>
                </tr>
                
                <tr>
                    <td>
                        <span class="page-type type-category">Category</span>
                        <div class="page-name">Exam Category</div>
                        <a href="exam-category.html" class="page-link html-link" target="_blank">exam-category.html</a>
                    </td>
                    <td>
                        <span class="page-type type-category">Category</span>
                        <div class="page-name">Exam Category</div>
                        <a href="exam-category.php" class="page-link php-link" target="_blank">exam-category.php</a>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="stats">
            <strong>Total Pages:</strong> 17 HTML pages converted to PHP | 
            <strong>Status:</strong> All pages successfully converted ✅
        </div>
        
        <div class="test-buttons">
            <a href="header.php" class="test-btn" target="_blank">📄 Test Header.php</a>
            <a href="footer.php" class="test-btn" target="_blank">📄 Test Footer.php</a>
            <a href="index.php" class="test-btn" target="_blank">🏠 Start from Home (PHP)</a>
        </div>
    </div>
    
    <script>
        // Add click tracking for debugging
        document.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function() {
                console.log('Clicked:', this.href);
            });
        });
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                window.open('index.html', '_blank');
            }
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.open('index.php', '_blank');
            }
        });
        
        // Show keyboard shortcuts info
        console.log('Keyboard Shortcuts:\nCtrl+H: Open HTML Home\nCtrl+P: Open PHP Home');
    </script>
</body>
</html>