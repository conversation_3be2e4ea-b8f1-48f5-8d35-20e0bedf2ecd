* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Section - Mobile App Style */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    position: relative;
}

.logo {
    font-size: 20px;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav {
    display: none;
}

.nav a {
    text-decoration: none;
    color: rgba(255,255,255,0.9);
    font-size: 15px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav a:hover {
    color: white;
    background: rgba(255,255,255,0.1);
}

.whatsapp-btn {
    background: rgba(255,255,255,0.15);
    color: white;
    padding: 10px 16px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.whatsapp-btn:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Mobile Menu - App Style */
.mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.15);
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 10px;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.mobile-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.mobile-menu-btn:hover::before {
    left: 100%;
}

.mobile-menu-btn:hover {
    background: rgba(255,255,255,0.25);
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.mobile-menu-btn:active {
    transform: scale(0.95);
}


.mobile-menu-btn i {
    font-size: 18px;
}

.nav-mobile {
    display: block;
    position: fixed;
    top: 0;
    left: -320px;
    width: 320px;
    height: 100vh;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    box-shadow: 4px 0 25px rgba(0,0,0,0.25);
    z-index: 1001;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    padding-top: 0;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-mobile.active {
    display: block;
    left: 0;
    box-shadow: 4px 0 30px rgba(0,0,0,0.3);
}

/* Mobile menu overlay */
.nav-mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.nav-mobile-overlay.active {
    display: block;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Prevent body scroll when mobile menu is open */
body.menu-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* Smooth slide-in animation for mobile nav */
.nav-mobile {
    transform: translateX(-100%);
}

.nav-mobile.active {
    transform: translateX(0);
}

/* Desktop styles - revert to original design */
@media (min-width: 769px) {
    .header {
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header-content {
        padding: 20px 0;
    }
    
    .logo {
        color: #2c5aa0;
        font-size: 24px;
    }
    
    .logo img {
        filter: none;
    }
    
    .nav {
        display: flex;
        gap: 30px;
    }
    
    .nav a {
        color: #555;
    }
    
    .nav a:hover {
        color: #2c5aa0;
        background: #f8f9fa;
    }
    
    .mobile-menu-btn {
        display: none;
    }
    
    .whatsapp-btn {
        background: #25d366;
        border: none;
        backdrop-filter: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-size: 14px;
    }
    
    .whatsapp-btn:hover {
        background: #128c7e;
        transform: none;
        box-shadow: none;
    }
    
    .nav-mobile {
        display: none !important;
    }
    
    .nav-mobile.active {
        display: block !important;
        left: 0 !important;
        transform: translateX(0) !important;
        z-index: 9999 !important;
    }
    
    .nav-mobile-overlay {
        display: none !important;
    }
    
    .nav-mobile-overlay.active {
        display: block !important;
        z-index: 9998 !important;
    }
}

/* Global override for mobile navigation when active - works on all screen sizes */
.nav-mobile.active {
    display: block !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 300px !important;
    height: 100vh !important;
    z-index: 9999 !important;
    transform: translateX(0) !important;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%) !important;
}

.nav-mobile-overlay.active {
    display: block !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0,0,0,0.5) !important;
    z-index: 9998 !important;
}

.nav-mobile a {
    display: flex;
    align-items: center;
    padding: 14px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-decoration: none;
    color: rgba(255,255,255,0.9);
    font-size: 15px;
    font-weight: 500;
    gap: 15px;
    transition: all 0.3s ease;
    min-height: 50px;
    position: relative;
    overflow: hidden;
    margin: 2px 15px;
    border-radius: 12px;
    border-bottom: none;
}

.nav-mobile a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: -1;
    border-radius: 15px;
}

.nav-mobile a:hover {
    color: white;
    transform: translateX(3px);
}

.nav-mobile a:hover::before {
    transform: translateX(0);
}

.nav-mobile a i {
    font-size: 16px;
    width: 20px;
    text-align: center;
    color: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
}

.nav-mobile a:hover i {
    color: white;
    transform: scale(1.1);
}

.nav-mobile a:last-child {
    border-bottom: none;
}

.nav-mobile .whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px 20px;
    padding: 12px 18px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    background: #25d366;
    color: white;
    text-decoration: none;
    gap: 6px;
    transition: all 0.3s ease;
    border: 2px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.nav-mobile .whatsapp-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-mobile .whatsapp-btn:hover::before {
    left: 100%;
}

.nav-mobile .whatsapp-btn:hover {
    background: #20c65a;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

.nav-mobile .whatsapp-btn:active {
    transform: translateY(0) scale(1);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.nav-mobile .whatsapp-btn i {
    color: white;
    font-size: 16px;
    animation: pulse 2s infinite;
}

/* Enhanced Mobile Menu Header */
.nav-mobile-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.nav-mobile-logo {
    color: white;
    font-size: 16px;
    font-weight: 700;
}

.nav-mobile-close {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    font-size: 16px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-mobile-close:hover {
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Search Section */
.search-section {
    padding: 50px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    z-index: 1;
}

.search-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.search-title {
    color: white;
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 8px;
}

.search-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 30px;
}

.search-box {
    position: relative;
    margin: 0 auto;
    max-width: 600px;
}

.search-box input {
    width: 100%;
    padding: 18px 65px 18px 25px;
    border: none;
    border-radius: 50px;
    font-size: 15px;
    outline: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.search-box input:focus {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    background: white;
    transform: translateY(-2px);
}

.search-box input::placeholder {
    color: #888;
}

.search-box button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.search-box button:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.search-box button:active {
    transform: translateY(-50%) scale(0.95);
}


/* Browse Icons Section */
.browse-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #333;
}

.browse-section .section-title {
    font-size: 26px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40px;
    padding-top: 20px;
    position: relative;
}

.browse-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    border-radius: 2px;
}

.section-subtitle {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
}

.notifications-section .section-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40px;
    padding-top: 20px;
    position: relative;
}

.notifications-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.notifications-section .section-subtitle {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-bottom: 30px;
}

.browse-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
}

.browse-item {
    background: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    text-decoration: none;
    color: #555;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;
}

.browse-item:hover {
    border: 1px solid #667eea;
}

.browse-item i {
    font-size: 24px;
    margin-bottom: 12px;
    padding: 15px;
    border-radius: 50%;
    background: var(--item-bg);
    color: var(--item-color);
    width: 54px;
    height: 54px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--item-shadow);
}


.browse-item h3 {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

/* Individual category styling */
.browse-item:nth-child(1) {
    --item-color: #667eea;
    --item-bg: rgba(102, 126, 234, 0.1);
    --item-shadow: rgba(102, 126, 234, 0.2);
}

.browse-item:nth-child(2) {
    --item-color: #f5576c;
    --item-bg: rgba(245, 87, 108, 0.1);
    --item-shadow: rgba(245, 87, 108, 0.2);
}

.browse-item:nth-child(3) {
    --item-color: #4facfe;
    --item-bg: rgba(79, 172, 254, 0.1);
    --item-shadow: rgba(79, 172, 254, 0.2);
}

.browse-item:nth-child(4) {
    --item-color: #43e97b;
    --item-bg: rgba(67, 233, 123, 0.1);
    --item-shadow: rgba(67, 233, 123, 0.2);
}

.browse-item:nth-child(5) {
    --item-color: #6b73ff;
    --item-bg: rgba(107, 115, 255, 0.1);
    --item-shadow: rgba(107, 115, 255, 0.2);
}

/* Notifications Section */
.notifications-section {
    padding: 50px 0;
    background: linear-gradient(135deg, #f8faff 0%, #e8f2ff 100%);
    position: relative;
}

.notifications-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23667eea' fill-opacity='0.02'%3E%3Cpath d='M20 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0-20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z'/%3E%3C/g%3E%3C/svg%3E") repeat;
    z-index: 1;
}

.notifications-section .container {
    position: relative;
    z-index: 2;
}

.notifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.notification-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    position: relative;
    display: block;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    display: flex;
    flex-direction: column;
}

.notification-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.notification-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 18px;
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f4ff;
    color: #667eea;
    font-size: 14px;
    margin-right: 15px;
    flex-shrink: 0;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    flex: 1;
    margin: 0;
}

.notification-meta {
    margin-bottom: 12px;
}

.notification-date {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
    gap: 5px;
    justify-content: flex-start;
    text-align: left;
}

.notification-date i {
    color: #aaa;
}

.notification-desc {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
    flex-grow: 1;
}

.notification-footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
    margin-top: 6px;
}

.notifications-section .notification-date {
    text-align: left;
}

.notifications-section .notification-link {
    margin-top: 0;
    margin-left: auto;
    text-align: right;
}

.notification-link {
    color: #667eea;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
    z-index: 2;
    background: #f8faff;
}

.notification-link:hover {
    background: #667eea;
    color: white;
}

.notification-link:active {
    z-index: 3;
}

/* Study Materials Section */
.study-materials-section {
    background: white;
}

.study-materials-section .container {
    padding: 0;
}

.study-materials-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 30px;
}

.study-section {
    background: #fafbfc;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #f0f0f0;
}

.study-section h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.study-section h3 i {
    color: #667eea;
    font-size: 18px;
}

.study-item {
    background: white;
    border-radius: 8px;
    padding: 18px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid #f5f5f5;
}

.study-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.study-item:last-child {
    margin-bottom: 0;
}

.study-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.study-item-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    flex: 1;
    margin-right: 10px;
}

.study-item-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff2f2;
    color: #e74c3c;
    font-size: 14px;
    flex-shrink: 0;
}

.exam-item-icon {
    background: #f0fff4;
    color: #27ae60;
}

.study-item-meta {
    font-size: 11px;
    color: #888;
    margin-bottom: 10px;
}

.study-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.study-item-date {
    font-size: 11px;
    color: #999;
}

.study-action-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.study-action-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.exam-action-btn {
    background: #27ae60;
    color: white;
}

.exam-action-btn:hover {
    background: #229954;
}


/* New Study Materials Column Layout */
.study-column {
    padding: 25px;
}

.materials-column {
    background: #f8f9fa;
}

.exams-column {
    background: #f5f6f7;
}

.column-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255,255,255,0.5);
}

.column-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.materials-column .column-header h3 i {
    color: #2c5aa0;
}

.exams-column .column-header h3 i {
    color: #229954;
}

.study-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.study-card {
    background: white;
    border-radius: 10px;
    padding: 15px;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.study-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    text-decoration: none;
    color: inherit;
}

.card-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.materials-column .card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.exams-column .card-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.card-icon i {
    font-size: 18px;
}

.card-content {
    flex: 1;
}

.card-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
    line-height: 1.3;
}

.card-content p {
    font-size: 12px;
    color: #666;
    margin: 0 0 5px 0;
    line-height: 1.4;
}

.card-meta {
    font-size: 11px;
    color: #888;
}

/* New Single Row Design for Study Materials and Exams */
.materials-section {
    background: white;
    padding: 40px 0;
}

.exams-section {
    background: #f8f9fa;
    padding: 40px 0;
}

.materials-grid,
.exams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.material-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.exam-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.2s ease;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    cursor: pointer;
}

.material-card:hover {
    border: 1px solid #667eea;
    text-decoration: none;
    color: inherit;
}

.exam-card:hover {
    border: 1px solid #667eea;
    text-decoration: none;
    color: inherit;
}

.material-icon {
    width: 50px;
    height: 50px;
    background: #f0f4ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.material-icon i {
    color: #667eea;
    font-size: 20px;
}

.exam-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}


.exam-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f4ff;
    color: #667eea;
    font-size: 14px;
    margin-right: 15px;
    flex-shrink: 0;
}

.exam-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.3;
    flex: 1;
}


.material-content {
    flex: 1;
}



.exam-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.exam-meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #888;
}

.exam-meta-item i {
    color: #667eea;
    font-size: 11px;
}

.exam-date {
    font-size: 11px;
    color: #999;
    margin: 0;
}

.exam-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

.take-exam-btn {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: #f0f4ff;
    border: 1px solid #667eea;
    cursor: pointer;
    display: inline-block;
}

.take-exam-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.material-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 6px 0;
    line-height: 1.3;
}

.material-content p {
    font-size: 13px;
    color: #666;
    margin: 0 0 6px 0;
    line-height: 1.4;
}

.material-meta {
    font-size: 12px;
    color: #888;
}

/* Content Sections */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

/* Blog Section */
.blog-section {
    padding: 50px 0;
    background: #f8faff;
}

.blog-section-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 15px;
    position: relative;
}

.blog-section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-radius: 2px;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.blog-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    text-decoration: none;
    color: inherit;
    display: block;
}

.blog-card:hover {
    text-decoration: none;
    color: inherit;
}

.blog-card:hover .blog-title {
    text-decoration: underline;
}

.blog-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}


.blog-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.blog-content {
    padding: 15px;
}

.blog-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 2.8em;
}

.blog-excerpt {
    font-size: 13px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.blog-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 11px;
    color: #888;
}

.blog-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.blog-meta i {
    color: #aaa;
}

.blog-read-more {
    display: inline-flex;
    align-items: center;
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.blog-read-more:hover {
    color: #5a6fd8;
    transform: translateX(3px);
}

.blog-read-more::after {
    content: '→';
    margin-left: 5px;
    transition: transform 0.2s ease;
}

.blog-read-more:hover::after {
    transform: translateX(3px);
}

/* Exam Categories Section */
.categories-section {
    background: white;
    padding: 50px 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 1200px) {
    .categories-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.category-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-direction: row;
    align-items: center;
    text-align: left;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    cursor: pointer;
}

.category-card:hover {
    border: 1px solid #667eea;
    text-decoration: none;
    color: inherit;
}

.category-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f4ff;
    color: #667eea;
    font-size: 14px;
    margin-right: 15px;
    flex-shrink: 0;
}

.category-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.3;
    flex: 1;
}

.category-arrow {
    display: none;
    color: #999;
    font-size: 14px;
    margin-left: 10px;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 0;
}

.footer-main {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 40px;
    align-items: center;
    background: #34495e;
    padding: 30px 20px;
    margin-bottom: 0;
}

.footer-logo {
    text-align: center;
}

.footer-logo h2 {
    font-size: 28px;
    font-weight: 700;
    color: white;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.footer-logo h2 i {
    font-size: 32px;
    color: #43e97b;
}

.footer-tagline {
    font-size: 14px;
    color: #cbd5e0;
    margin: 0;
}

.footer-groups {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.group-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 15px;
    text-align: center;
}

.group-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.group-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 25px;
    border-radius: 30px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 180px;
    justify-content: center;
    box-sizing: border-box;
}

.footer-groups .whatsapp-btn {
    background: #25d366 !important;
    color: white !important;
    border: 2px solid #25d366 !important;
}

.footer-groups .whatsapp-btn:hover {
    background: #128c7e !important;
    border-color: #128c7e !important;
    color: white !important;
    text-decoration: none !important;
}

.footer-groups .telegram-btn {
    background: #0088cc !important;
    color: white !important;
    border: 2px solid #0088cc !important;
}

.footer-groups .telegram-btn:hover {
    background: #005788 !important;
    border-color: #005788 !important;
    color: white !important;
    text-decoration: none !important;
}

.footer-social {
    text-align: center;
}

.social-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 18px;
    transition: all 0.3s ease;
    color: white;
}

.social-facebook {
    background: #3b5998;
}

.social-twitter {
    background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
}

.social-instagram {
    background: #e4405f;
}

.social-youtube {
    background: #ff0000;
}

.social-linkedin {
    background: #0077b5;
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.footer-links {
    background: #2c3e50;
    padding: 30px 20px;
    margin: 0;
}

.footer-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.footer-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #cbd5e0;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding: 20px;
    font-size: 14px;
    color: #bdc3c7;
    background: #1a252f;
}

/* Responsive Design */
/* Mobile App Header Design */
@media (max-width: 768px) {
    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 0;
    }
    
    .header-content {
        position: relative;
        justify-content: space-between;
        align-items: center;
        padding: 8px 15px;
    }

    .logo {
        position: static;
        transform: none;
        flex: 1;
        text-align: center;
        font-size: 16px;
        font-weight: 700;
        color: white;
    }
    
    .logo img {
        height: 30px;
        filter: brightness(0) invert(1);
    }

    .mobile-menu-btn {
        position: static;
        transform: none;
        order: -1;
    }

    .nav {
        display: none;
    }

    .whatsapp-btn {
        padding: 6px 10px;
        font-size: 11px;
        border-radius: 18px;
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
    }

    .search-box {
        width: 95%;
    }

    .browse-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .browse-item {
        display: flex;
        align-items: center;
        text-align: left;
        padding: 15px 20px;
    }

    .browse-item i {
        margin: 0 15px 0 0;
        flex-shrink: 0;
    }

    .browse-item h3 {
        margin: 0;
        flex: 1;
    }

    .notifications-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .notifications-section .section-title {
        font-size: 22px;
    }

    .study-materials-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .category-arrow {
        display: block;
    }

    .footer-main {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-groups {
        width: 100%;
        padding: 0 10px;
    }

    .footer-groups .group-buttons {
        display: block;
        width: 100%;
        text-align: center;
    }

    .footer-groups .whatsapp-btn,
    .footer-groups .telegram-btn {
        display: inline-block;
        width: calc(50% - 10px);
        font-size: 12px;
        padding: 10px 5px;
        margin: 5px 5px;
        text-align: center;
        vertical-align: top;
        box-sizing: border-box;
        white-space: nowrap;
    }

    .footer-links-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .footer-groups .whatsapp-btn,
    .footer-groups .telegram-btn {
        display: inline-block !important;
        width: calc(50% - 8px) !important;
        margin: 4px 4px !important;
        padding: 8px 4px !important;
        font-size: 11px !important;
        white-space: nowrap !important;
    }
}

@media (min-width: 769px) {
    .search-box {
        width: 70%;
    }
}

/* Blog Section Additional Styles */
.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40px;
    padding-top: 20px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 16px;
    color: #666;
    max-width: 600px;
    margin: 20px auto 40px auto;
    line-height: 1.5;
}

.blog-author,
.blog-date,
.blog-read-time {
    font-size: 11px;
    color: #888;
}

.blog-footer {
    text-align: center;
    margin-top: 50px;
}

.view-all-blogs {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.view-all-blogs:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Blog Section Mobile Responsiveness */
@media (max-width: 768px) {
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .blog-meta {
        flex-wrap: wrap;
        gap: 10px;
    }

    .section-title {
        font-size: 22px;
    }

    .section-subtitle {
        font-size: 14px;
        padding: 0 20px;
    }

    .materials-grid,
    .exams-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Banner Ad Styles */
.banner-ad {
    text-align: center;
    margin: 30px 0;
    padding: 20px 0;
}

.banner-ad img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.banner-ad img:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.banner-ad-large {
    max-width: 728px;
    margin: 40px auto;
}

.banner-ad-medium {
    max-width: 468px;
    margin: 30px auto;
}

.banner-ad-small {
    max-width: 300px;
    margin: 20px auto;
}

/* Responsive banner ads */
@media (max-width: 768px) {
    .banner-ad {
        margin: 20px 0;
        padding: 15px 0;
    }
    
    .banner-ad-large,
    .banner-ad-medium {
        max-width: 100%;
        padding: 0 20px;
    }
    
    .banner-ad img {
        border-radius: 6px;
    }
}

/* Notifications Page Styles */
.page-header {
    padding: 40px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    z-index: 1;
}

.page-header-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.page-title {
    color: white;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    margin-bottom: 0;
}


/* Notifications List Section */
.notifications-list-section {
    background: #f8f9fa;
    padding: 30px 0;
    min-height: 60vh;
}

.notifications-count {
    margin-bottom: 25px;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.notifications-count span {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.notifications-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.notification-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.notification-item:hover .notification-card-simple:not(.clickable-card) {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* Simplified notification card styles */
.notification-card-simple {
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.clickable-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.clickable-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.clickable-card:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
}

.clickable-card:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.clickable-card:focus:not(:focus-visible) {
    outline: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f4ff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2px;
    border: 1px solid #e0e6ff;
}

.notification-icon i {
    color: #667eea;
    font-size: 16px;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 0;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    flex: 1;
    margin: 0;
}

.clickable-card:hover .notification-title {
    text-decoration: underline;
}

.notification-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin: 8px 0 12px 0;
}

.notification-footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
    margin-top: 6px;
}

.notifications-section .notification-date {
    text-align: left;
}

.notifications-section .notification-link {
    margin-top: 0;
    margin-left: auto;
    text-align: right;
}

.notification-date {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
    gap: 5px;
    justify-content: flex-start;
    text-align: left;
}

.notification-date i {
    color: #aaa;
    font-size: 10px;
}

.view-details-btn {
    color: #667eea;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 5px;
    transition: all 0.2s ease;
    background: #f0f4ff;
    border: 1px solid #667eea;
    white-space: nowrap;
}

.view-details-btn:hover {
    background: #667eea;
    color: white;
    text-decoration: none;
}


/* Pagination Styles */
.pagination-section {
    margin-top: 40px;
    padding: 30px 0;
    text-align: center;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-decoration: none;
    color: #555;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: white;
}

.pagination-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    text-decoration: none;
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
}

.pagination-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-decoration: none;
    color: #555;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: white;
}

.pagination-number:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
    text-decoration: none;
}

.pagination-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

/* Active navigation item */
.nav a.active {
    color: #667eea;
    background: #f0f4ff;
    font-weight: 600;
}

.nav-mobile a.active {
    color: white;
    background: rgba(255,255,255,0.2);
    font-weight: 600;
    border: 1px solid rgba(255,255,255,0.3);
}

.nav-mobile a.active::before {
    transform: translateX(0);
}

.nav-mobile a.active i {
    color: white;
    transform: scale(1.1);
}

/* Mobile Responsive Styles for Notifications Page */
@media (max-width: 768px) {
    .page-title {
        font-size: 24px;
    }
    
    .page-subtitle {
        font-size: 14px;
    }
    
    .notifications-list-section {
        padding: 20px 0;
    }
    
    .notifications-count {
        margin-bottom: 20px;
    }
    
    .notifications-list {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .notification-card-simple {
        padding: 15px;
    }
    
    .notification-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
    }
    
    .notification-icon i {
        font-size: 16px;
    }
    
    .notification-content {
        gap: 8px;
    }
    
    .notification-header {
        gap: 8px;
        align-items: flex-start;
    }
    
    .notification-title {
        font-size: 14px;
        line-height: 1.4;
    }
    
    .notification-date {
        font-size: 12px;
    }
    
    .notification-date i {
        font-size: 10px;
    }
    
    .notification-description {
        font-size: 13px;
        line-height: 1.4;
    }
    
    .notification-footer {
        margin-top: 10px;
    }
    
    .view-details-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .pagination-section {
        margin-top: 30px;
    }
    
    .pagination {
        gap: 5px;
    }
    
    .pagination-numbers {
        gap: 3px;
    }
    
    .pagination-number {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
    
    .prev-btn {
        padding: 6px 10px;
    }
    
    .next-btn {
        padding: 6px 10px;
    }
    
    .pagination-btn {
        font-size: 12px;
        padding: 6px 10px;
        min-width: auto;
    }
    
    .pagination-info {
        font-size: 12px;
        margin-top: 10px;
    }
    
    .notification-content-wrapper {
        flex-direction: column;
    }
    
    .notification-main-content {
        width: 100%;
    }
    
    .notification-single-title {
        font-size: 16px;
        line-height: 1.4;
    }
    
    .notification-meta-info {
        flex-direction: column;
        gap: 8px;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-bottom: 10px;
    }
    
    .notification-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .action-btn {
        width: 100%;
    }
    
    .breadcrumb-wrapper {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .breadcrumb {
        flex-wrap: wrap;
        gap: 6px;
        font-size: 11px;
        justify-content: flex-start;
        align-items: center;
    }
    
    .breadcrumb-item {
        padding: 3px 6px;
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 3px;
    }
    
    .breadcrumb-current {
        padding: 3px 6px;
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 3px;
    }
    
    .breadcrumb-actions {
        width: 100%;
    }
    
    .back-btn {
        width: 100%;
        padding: 8px 12px;
        font-size: 13px;
        justify-content: center;
    }
    
    /* Breadcrumb mobile improvements */
    .breadcrumb-section {
        padding: 12px 0;
    }
    
    .breadcrumb-item i,
    .breadcrumb-current i {
        font-size: 9px;
        margin-right: 2px;
    }
    
    .breadcrumb-arrow {
        font-size: 7px;
        margin: 0 2px;
        color: #999;
    }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
    .notification-card-simple {
        padding: 12px;
        gap: 10px;
    }
    
    .notification-title {
        font-size: 15px;
    }
    
    .notification-description {
        font-size: 12px;
    }
    
    .notification-icon {
        width: 28px;
        height: 28px;
    }
    
    .notification-icon i {
        font-size: 12px;
    }
    
    .view-details-btn {
        padding: 5px 12px;
        font-size: 11px;
    }
    
    .notification-date {
        font-size: 10px;
    }
    
    .pagination-btn {
        max-width: 150px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .pagination-number {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

/* Single Notification Page Styles */
.breadcrumb-section {
    background: #fafbfc;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #667eea;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item:hover {
    color: #5a6fd8;
    text-decoration: none;
}

.breadcrumb-item i {
    font-size: 12px;
}

.breadcrumb-arrow {
    color: #999;
    font-size: 10px;
    margin: 0 4px;
}

.breadcrumb-current {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-weight: 500;
}

.breadcrumb-current i {
    font-size: 12px;
}

.breadcrumb-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: white;
    color: #667eea;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #f0f4ff;
    border-color: #667eea;
}

.back-btn i {
    font-size: 11px;
}

.single-notification-section {
    padding: 30px 0;
    background: #f8f9fa;
}

.notification-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
}

.notification-main-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.notification-single-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.notification-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.active {
    background: #e8f5e8;
    color: #2d7a2d;
}

.notification-id {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.notification-single-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
    margin-bottom: 20px;
}

.notification-meta-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #555;
}

.meta-item i {
    color: #667eea;
    width: 16px;
}

.section-block {
    margin-bottom: 30px;
}

.section-block h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

.section-block p {
    font-size: 15px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 15px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.detail-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.detail-card h3 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.detail-card p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.eligibility-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 20px 0 10px 0;
}

.eligibility-section ul {
    margin: 0 0 20px 20px;
    padding: 0;
}

.eligibility-section li {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.5;
}

.dates-table {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.date-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.date-row:last-child {
    border-bottom: none;
}

.date-label {
    font-size: 14px;
    color: #555;
    font-weight: 500;
}

.date-value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.important-date {
    color: #e74c3c;
}

.process-steps {
    margin-top: 20px;
}

.step {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.step-content p {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.primary-btn {
    background: #667eea;
    color: white;
    border: 1px solid #667eea;
}

.primary-btn:hover {
    background: #5a6fd8;
    text-decoration: none;
    color: white;
}

.secondary-btn {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
}

.secondary-btn:hover {
    background: #f0f4ff;
    text-decoration: none;
    color: #667eea;
}

/* Sidebar Styles */
.notification-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sidebar-widget {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.sidebar-widget h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.quick-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-item .label {
    font-size: 12px;
    color: #888;
    font-weight: 500;
}

.info-item .value {
    font-size: 13px;
    color: #333;
    font-weight: 600;
}

.sidebar-apply-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.sidebar-apply-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    width: 100%;
}

.sidebar-apply-btn:hover {
    background: #5a6fd8;
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
}

.sidebar-apply-btn i {
    font-size: 12px;
}

.related-notifications {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.related-item {
    display: block;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.related-item:hover {
    background: #f0f4ff;
    text-decoration: none;
    color: inherit;
}

.related-item h4 {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.3;
}

.related-item .date {
    font-size: 11px;
    color: #888;
}

.help-section p {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}

.help-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #f8f9fa;
    color: #667eea;
    text-decoration: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.help-btn:hover {
    background: #f0f4ff;
    text-decoration: none;
    color: #667eea;
}

/* Mobile Responsive Styles for Single Notification */
@media (max-width: 768px) {
    .notification-content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .notification-main-content {
        padding: 20px;
    }
    
    .notification-single-title {
        font-size: 22px;
    }
    
    .notification-meta-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .step {
        gap: 15px;
    }
    
    .step-number {
        width: 35px;
        height: 35px;
    }
    
    .notification-actions {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
    
    .breadcrumb-wrapper {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .breadcrumb {
        font-size: 12px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .breadcrumb-item {
        font-size: 11px;
    }
    
    .breadcrumb-current {
        word-break: break-word;
        font-size: 11px;
        text-align: center;
    }
    
    .breadcrumb-actions {
        justify-content: center;
    }
    
    .back-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}

/* Share Section Styles */
.share-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.share-section h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 500;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 40px;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 18px;
}

.whatsapp-share {
    background: #25d366;
    color: white;
}

.whatsapp-share:hover {
    background: #128c7e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.telegram-share {
    background: #0088cc;
    color: white;
}

.telegram-share:hover {
    background: #006699;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.3);
}

.facebook-share {
    background: #1877f2;
    color: white;
}

.facebook-share:hover {
    background: #166fe5;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

@media (max-width: 768px) {
    .share-buttons {
        gap: 12px;
    }
    
    .share-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

.breadcrumb-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

/* Single Study Material Page Styles */
.single-study-material-section {
    padding: 40px 0;
    background: #fafbfc;
}

.study-material-content-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
}

.study-material-main-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.study-material-header {
    padding: 30px;
    border-bottom: 1px solid #e9ecef;
}

.study-material-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.3;
}

.study-material-meta-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.study-material-details-content {
    padding: 30px;
}

.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.topic-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.topic-card h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.topic-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.topic-card li {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
    padding-left: 15px;
    position: relative;
}

.topic-card li::before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.features-list {
    margin-top: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.feature-item i {
    color: #28a745;
    font-size: 16px;
}

.feature-item span {
    font-size: 14px;
    color: #333;
}

.download-section {
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.download-section h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.download-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.download-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.download-btn.primary-btn {
    background: #667eea;
    color: white;
}

.download-btn.primary-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.download-btn.secondary-btn {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.download-btn.secondary-btn:hover {
    background: #667eea;
    color: white;
}

.download-note {
    font-size: 12px;
    color: #888;
    margin: 0;
}

.related-materials-section {
    margin-top: 40px;
}

.related-materials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.related-material-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.related-material-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.material-icon {
    width: 50px;
    height: 50px;
    background: #f0f4ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.material-icon i {
    color: #667eea;
    font-size: 20px;
}

.related-material-card h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.related-material-card p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.study-material-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.study-tips {
    margin-top: 15px;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 12px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.tip-item i {
    color: #ffc107;
    font-size: 14px;
    margin-top: 2px;
}

.tip-item p {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Mobile Responsive Styles for Single Study Material */
@media (max-width: 768px) {
    .study-material-content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .study-material-main-content {
        padding: 20px;
    }
    
    .study-material-title {
        font-size: 22px;
    }
    
    .study-material-meta-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .topics-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .download-options {
        flex-direction: column;
        align-items: center;
    }
    
    .related-materials-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .breadcrumb-wrapper {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .breadcrumb {
        font-size: 12px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .breadcrumb-item {
        font-size: 11px;
    }
    
    .breadcrumb-current {
        word-break: break-word;
        font-size: 11px;
        text-align: center;
    }
    
    .breadcrumb-actions {
        justify-content: center;
    }
    
    .back-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    /* Mobile-specific padding optimizations */
    .single-study-material-section {
        padding: 20px 0;
    }
    
    .study-material-header {
        padding: 20px 15px;
    }
    
    .study-material-details-content {
        padding: 15px;
    }
    
    .section-block {
        margin-bottom: 20px;
    }
    
    .section-block h2 {
        font-size: 18px;
        margin-bottom: 12px;
    }
    
    .section-block p {
        font-size: 14px;
        line-height: 1.6;
    }
    
    .section-block ul {
        padding-left: 20px;
    }
    
    .section-block li {
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.5;
    }
    
    .download-section {
        margin-top: 20px;
        padding: 20px 15px;
    }
    
    .download-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .materials-section {
        padding: 30px 0;
    }
    
    .materials-section .container {
        padding: 0 15px;
    }
    
    .materials-grid {
        gap: 15px;
    }
    
    .material-card {
        padding: 15px;
    }
    
    .material-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 12px;
    }
    
    .material-icon i {
        font-size: 16px;
    }
    
    .material-content h4 {
        font-size: 15px;
        margin-bottom: 8px;
    }
    
    .material-content p {
        font-size: 13px;
        margin-bottom: 8px;
    }
    
    .material-meta {
        font-size: 11px;
    }
    
    .share-section {
        padding: 15px 0;
    }
    
    .share-section h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }
    
    .share-buttons {
        gap: 10px;
    }
    
    .share-btn {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
    
    .banner-ad {
        margin: 15px 0;
    }
    
    .banner-ad img {
        border-radius: 6px;
    }
}

@media (max-width: 480px) {
    .study-material-main-content {
        padding: 10px;
    }
    
    .study-material-header {
        padding: 15px 10px;
    }
    
    .study-material-title {
        font-size: 20px;
        line-height: 1.3;
    }
    
    .study-material-details-content {
        padding: 10px;
    }
    
    .section-block h2 {
        font-size: 16px;
    }
    
    .section-block p,
    .section-block li {
        font-size: 13px;
    }
    
    .download-section {
        padding: 15px 10px;
    }
    
    .download-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
    
    .materials-section {
        padding: 20px 0;
    }
    
    .materials-section .container {
        padding: 0 10px;
    }
    
    .material-card {
        padding: 12px;
    }
    
    .material-icon {
        width: 35px;
        height: 35px;
        margin-bottom: 10px;
    }
    
    .material-icon i {
        font-size: 14px;
    }
    
    .material-content h4 {
        font-size: 14px;
    }
    
    .material-content p {
        font-size: 12px;
    }
    
    .material-meta {
        font-size: 10px;
    }
    
    .share-section {
        padding: 10px 0;
    }
    
    .share-section h3 {
        font-size: 14px;
    }
    
    .share-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
    
    .banner-ad {
        margin: 10px 0;
    }
}

/* --- Enhanced Mobile Responsive Header Design --- */

/* Mobile Header Layout */
.mobile-header {
    display: none;
}

.desktop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

/* Mobile Header Components */
.header-left,
.header-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.mobile-menu-btn,
.search-btn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 18px;
    height: 44px;
    width: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.mobile-menu-btn:hover,
.search-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.mobile-menu-btn:active,
.search-btn:active {
    transform: scale(0.95);
}

/* Full Screen Mobile Search Overlay */
.mobile-search-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #ffffff;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-search-overlay.active {
    display: block;
    opacity: 1;
    visibility: visible;
}

.mobile-search-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mobile-search-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    gap: 12px;
}

.mobile-search-back {
    background: none;
    border: none;
    color: #667eea;
    font-size: 20px;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.mobile-search-back:hover {
    background: rgba(102, 126, 234, 0.1);
}

.mobile-search-input-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.mobile-search-input {
    width: 100%;
    font-size: 16px;
    padding: 12px 40px 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    background: #f8f9fa;
    outline: none;
    transition: all 0.3s ease;
}

.mobile-search-input:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.mobile-search-clear {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: #94a3b8;
    font-size: 16px;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
    visibility: hidden;
}

.mobile-search-clear.visible {
    opacity: 1;
    visibility: visible;
}

.mobile-search-clear:hover {
    background: rgba(148, 163, 184, 0.1);
    color: #64748b;
}

/* Search Suggestions */
.mobile-search-suggestions {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 12px;
}

.search-suggestion-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.search-suggestion-item i {
    color: #94a3b8;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.search-suggestion-item span {
    color: #334155;
    font-size: 15px;
    font-weight: 500;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .mobile-header {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        min-height: 60px;
    }

    .desktop-header {
        display: none !important;
    }

    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        position: sticky;
        top: 0;
        z-index: 1000;
    }

    .logo img {
        height: 36px;
        filter: brightness(0) invert(1);
    }

    .mobile-search-overlay {
        display: block;
    }

    /* Ensure buttons are properly sized on smaller screens */
    .mobile-menu-btn,
    .search-btn {
        height: 40px;
        width: 40px;
        font-size: 16px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .mobile-header {
        padding: 10px 12px;
        min-height: 56px;
    }

    .logo img {
        height: 32px;
    }

    .mobile-menu-btn,
    .search-btn {
        height: 38px;
        width: 38px;
        font-size: 15px;
    }

    .mobile-search-header {
        padding: 10px 12px;
    }

    .mobile-search-input {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 10px 36px 10px 14px;
    }
}

@media (min-width: 769px) {
    .mobile-header {
        display: none !important;
    }

    .desktop-header {
        display: flex !important;
    }

    .mobile-search-overlay {
        display: none !important;
    }

    .header {
        background: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .logo {
        color: #2c5aa0;
        font-size: 24px;
    }

    .logo img {
        filter: none;
        height: 40px;
    }

    .nav {
        display: flex;
        gap: 30px;
    }

    .nav a {
        color: #555;
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .nav a:hover {
        color: #2c5aa0;
        background: #f8f9fa;
    }

    .nav a.active {
        color: #2c5aa0;
        background: #e8f2ff;
    }

    .whatsapp-btn {
        background: #25d366;
        color: white;
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .whatsapp-btn:hover {
        background: #128c7e;
    }
}

.header {
    position: sticky;
    top: 0;
    z-index: 1002;
}