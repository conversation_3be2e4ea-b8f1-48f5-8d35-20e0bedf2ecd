    <!-- Footer Section -->
    <footer class="footer">
        <div class="container">
            <div class="footer-main">
                <div class="footer-logo">
                    <h2><span style="font-size: 1.5em; font-weight: 700;">DRX</span> <span style="font-size: 0.6em; font-weight: 400;">Pharma Academy</span></h2>
                    <p class="footer-tagline">Your pathway to success in competitive exams</p>
                </div>
                
                <div class="footer-groups">
                    <h3 class="group-title">Join Our Study Groups</h3>
                    <div class="group-buttons">
                        <a href="#" class="group-btn whatsapp-btn">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp Group
                        </a>
                        <a href="#" class="group-btn telegram-btn">
                            <i class="fab fa-telegram"></i>
                            Telegram Channel
                        </a>
                    </div>
                </div>
                
                <div class="footer-social">
                    <h3 class="social-title">Follow Us</h3>
                    <div class="social-links">
                        <a href="#" class="social-link social-facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link social-instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link social-youtube">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link social-linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="footer-links">
                <div class="footer-links-grid">
                    <div class="footer-section">
                        <h3>Study Materials</h3>
                        <ul>
                            <li><a href="#">Pharmacology</a></li>
                            <li><a href="#">Pharmaceutical Chemistry</a></li>
                            <li><a href="#">Pharmacognosy</a></li>
                            <li><a href="#">Pharmaceutics</a></li>
                            <li><a href="#">Clinical Pharmacy</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Exam Prep</h3>
                        <ul>
                            <li><a href="#">GPAT Preparation</a></li>
                            <li><a href="#">NIPER JEE</a></li>
                            <li><a href="#">Drug Inspector</a></li>
                            <li><a href="#">Previous Papers</a></li>
                            <li><a href="#">Mock Tests</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Resources</h3>
                        <ul>
                            <li><a href="#">Study Notes</a></li>
                            <li><a href="#">Question Banks</a></li>
                            <li><a href="#">Video Lectures</a></li>
                            <li><a href="#">E-books</a></li>
                            <li><a href="#">Reference Materials</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Support</h3>
                        <ul>
                            <li><a href="contact.php">Contact Us</a></li>
                            <li><a href="about.php">About Us</a></li>
                            <li><a href="terms-conditions.php">Terms & Conditions</a></li>
                            <li><a href="privacy-policy.php">Privacy Policy</a></li>
                            <li><a href="sitemap.xml">Sitemap</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2024 DRX Pharma Academy. All rights reserved. | Designed for pharmacy competitive exam aspirants</p>
        </div>
    </footer>

    <!-- Mobile Bottom Navigation -->
    <nav class="mobile-bottom-nav">
        <a href="index.php" class="mobile-nav-item <?php echo (isset($current_page) && $current_page == 'home') ? 'active' : ''; ?>">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="archive-study-materials.php" class="mobile-nav-item <?php echo (isset($current_page) && $current_page == 'study-materials') ? 'active' : ''; ?>">
            <i class="fas fa-graduation-cap"></i>
            <span>Courses</span>
        </a>
        <a href="archive-exams.php" class="mobile-nav-item <?php echo (isset($current_page) && $current_page == 'exams') ? 'active' : ''; ?>">
            <i class="fas fa-clipboard-list"></i>
            <span>Exams</span>
        </a>
        <a href="practice.php" class="mobile-nav-item <?php echo (isset($current_page) && $current_page == 'practice') ? 'active' : ''; ?>">
            <i class="fas fa-question-circle"></i>
            <span>Practice</span>
        </a>
        <a href="about.php" class="mobile-nav-item <?php echo (isset($current_page) && $current_page == 'about') ? 'active' : ''; ?>">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
    </nav>

    <script src="script.js"></script>
    <?php if(isset($additional_js)) echo $additional_js; ?>

    <!-- Mobile Bottom Navigation Styles and Script -->
    <style>
        /* Mobile Bottom Navigation - Professional Design */
        .mobile-bottom-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #ffffff;
            border-top: 2px solid #e8eef7;
            box-shadow: 0 -3px 20px rgba(102, 126, 234, 0.08);
            padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
            z-index: 1000;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .mobile-bottom-nav {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            max-width: 100%;
            margin: 0 auto;
            padding-left: 16px;
            padding-right: 16px;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #64748b;
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 6px 8px;
            border-radius: 12px;
            min-width: 40px;
            position: relative;
            font-family: 'Poppins', sans-serif;
        }

        .mobile-nav-item i {
            font-size: 18px;
            margin-bottom: 2px;
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mobile-nav-item span {
            font-size: 10px;
            font-weight: 500;
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            letter-spacing: 0.02em;
        }

        .mobile-nav-item.active {
            color: #667eea;
            background: linear-gradient(145deg, rgba(102, 126, 234, 0.12), rgba(102, 126, 234, 0.08));
            transform: translateY(-1px);
        }

        .mobile-nav-item.active i {
            transform: scale(1.05);
            filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.2));
        }

        .mobile-nav-item.active span {
            font-weight: 600;
            color: #5a67d8;
        }

        .mobile-nav-item:hover:not(.active) {
            color: #475569;
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-0.5px);
        }

        /* Professional active indicator */
        .mobile-nav-item.active::before {
            content: '';
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        /* Subtle press effect */
        .mobile-nav-item:active {
            transform: scale(0.96) translateY(0);
            background: rgba(102, 126, 234, 0.15);
        }

        /* Show only on mobile devices */
        @media (max-width: 768px) {
            .mobile-bottom-nav {
                display: flex !important;
            }

            /* Add bottom padding to body to prevent content being hidden behind nav */
            body {
                padding-bottom: 60px;
            }
        }

        /* Hide on larger screens */
        @media (min-width: 769px) {
            .mobile-bottom-nav {
                display: none !important;
            }
            
            body {
                padding-bottom: 0;
            }
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 480px) {
            .mobile-bottom-nav {
                padding-left: 8px;
                padding-right: 8px;
            }
            
            .mobile-nav-item {
                padding: 6px 8px;
                min-width: 45px;
            }
            
            .mobile-nav-item span {
                font-size: 10px;
            }
            
            .mobile-nav-item i {
                font-size: 20px;
            }
        }

        /* Extra small devices */
        @media (max-width: 360px) {
            .mobile-nav-item {
                padding: 6px 6px;
                min-width: 40px;
            }
            
            .mobile-nav-item span {
                font-size: 9px;
                margin-top: 1px;
            }
            
            .mobile-nav-item i {
                font-size: 18px;
            }
        }

        /* Professional light theme consistency */
        @media (prefers-color-scheme: light) {
            .mobile-bottom-nav {
                background: #ffffff;
                border-top-color: #e8eef7;
            }
            
            .mobile-nav-item {
                color: #64748b;
            }
            
            .mobile-nav-item.active {
                color: #667eea;
                background: linear-gradient(145deg, rgba(102, 126, 234, 0.12), rgba(102, 126, 234, 0.08));
            }
        }


        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .mobile-bottom-nav {
                border-top-width: 3px;
                box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.5);
            }
            
            .mobile-nav-item.active {
                background: rgba(102, 126, 234, 0.2);
                border: 1px solid #667eea;
            }
        }
    </style>

    <script>
        // Mobile Bottom Navigation Enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            
            // Add haptic feedback for supported devices
            mobileNavItems.forEach(item => {
                item.addEventListener('touchstart', function() {
                    // Haptic feedback (if supported)
                    if ('vibrate' in navigator) {
                        navigator.vibrate(10);
                    }
                });
                
                // Add visual feedback
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });
                
                item.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });
            
            // Prevent scrolling when touching navigation
            const mobileBottomNav = document.querySelector('.mobile-bottom-nav');
            if (mobileBottomNav) {
                mobileBottomNav.addEventListener('touchmove', function(e) {
                    e.preventDefault();
                });
            }
        });
    </script>
</body>
</html>