<?php 
$page_title = "Archive Exams - DRX Pharma Academy";
$current_page = "exams";
$additional_js = '
    <script>
        // Exams counting and pagination functionality
        document.addEventListener("DOMContentLoaded", function() {
            // Count exams functionality
            const examCards = document.querySelectorAll(".exam-card");
            const examsCount = document.getElementById("examsCount");
            const paginationInfo = document.getElementById("paginationInfo");
            
            if (examCards.length > 0) {
                if (examsCount) {
                    examsCount.textContent = `Showing all exams`;
                }
                if (paginationInfo) {
                    paginationInfo.textContent = `Showing 1-6 of 30 exams`;
                }
            }
            
            // Add click tracking for exam cards
            examCards.forEach(card => {
                card.addEventListener("click", function(e) {
                    // Prevent button click from triggering link
                    if (e.target.classList.contains("take-exam-btn")) {
                        e.preventDefault();
                        console.log("Take Exam clicked:", this.querySelector(".exam-title").textContent);
                    } else {
                        console.log("Exam card clicked:", this.querySelector(".exam-title").textContent);
                    }
                });
            });
            
            // Take exam button functionality
            const takeExamBtns = document.querySelectorAll(".take-exam-btn");
            takeExamBtns.forEach(btn => {
                btn.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // Navigate to exam page
                    window.location.href = "single-exam.php";
                });
            });
            
            // Pagination functionality
            const paginationNumbers = document.querySelectorAll(".pagination-number");
            const prevBtn = document.getElementById("prevBtn");
            const nextBtn = document.getElementById("nextBtn");
            
            paginationNumbers.forEach(link => {
                link.addEventListener("click", function(e) {
                    e.preventDefault();
                    paginationNumbers.forEach(l => l.classList.remove("active"));
                    this.classList.add("active");
                    
                    // Update pagination info
                    const currentPage = parseInt(this.textContent);
                    const startItem = (currentPage - 1) * 6 + 1;
                    const endItem = Math.min(currentPage * 6, 30);
                    
                    if (paginationInfo) {
                        paginationInfo.textContent = `Showing ${startItem}-${endItem} of 30 exams`;
                    }
                    
                    // Update prev/next button styles
                    if (prevBtn) {
                        if (currentPage === 1) {
                            prevBtn.style.opacity = "0.5";
                            prevBtn.style.pointerEvents = "none";
                        } else {
                            prevBtn.style.opacity = "1";
                            prevBtn.style.pointerEvents = "auto";
                        }
                    }
                    
                    if (nextBtn) {
                        if (currentPage === 5) {
                            nextBtn.style.opacity = "0.5";
                            nextBtn.style.pointerEvents = "none";
                        } else {
                            nextBtn.style.opacity = "1";
                            nextBtn.style.pointerEvents = "auto";
                        }
                    }
                    
                    // Scroll to top
                    window.scrollTo({top: 0, behavior: "smooth"});
                });
            });
            
            // Previous/Next button functionality
            if (prevBtn) {
                prevBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    const activePage = document.querySelector(".pagination-number.active");
                    const currentPage = parseInt(activePage.textContent);
                    if (currentPage > 1) {
                        activePage.classList.remove("active");
                        const prevPage = document.querySelector(`[data-page="${currentPage - 1}"]`);
                        if (prevPage) {
                            prevPage.classList.add("active");
                            prevPage.click();
                        }
                    }
                });
            }
            
            if (nextBtn) {
                nextBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    const activePage = document.querySelector(".pagination-number.active");
                    const currentPage = parseInt(activePage.textContent);
                    if (currentPage < 5) {
                        activePage.classList.remove("active");
                        const nextPage = document.querySelector(`[data-page="${currentPage + 1}"]`);
                        if (nextPage) {
                            nextPage.classList.add("active");
                            nextPage.click();
                        }
                    }
                });
            }
            
            // Initialize first page state
            if (prevBtn) {
                prevBtn.style.opacity = "0.5";
                prevBtn.style.pointerEvents = "none";
            }
        });
    </script>';
include 'header.php'; 
?>

    <!-- Page Header Section -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    Archive Exams
                </h1>
                <p class="page-subtitle">Practice with our comprehensive collection of mock tests and model exams</p>
            </div>
        </div>
    </section>

    <!-- Banner Ad 1 - After Title Section -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner3.jpeg" alt="Advertisement">
    </div>

    <!-- Exams List Section -->
    <section class="notifications-list-section">
        <div class="container">
            <div class="notifications-count">
                <span id="examsCount">Showing all exams</span>
            </div>
            
            <div class="notifications-list" id="examsList">
                <!-- Exam Card 1 -->
                <div class="notification-item" data-category="gpat" data-subject="full-syllabus" data-date="2024-06-21">
                    <a href="single-exam.php" class="exam-card">
                        <div class="exam-header">
                            <div class="exam-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4 class="exam-title">GPAT Mock Test Series - 2024</h4>
                        </div>
                        <div class="exam-meta">
                            <div class="exam-meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>125 Questions</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>3 hours</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-book"></i>
                                <span>Full Syllabus</span>
                            </div>
                        </div>
                        <div class="exam-footer">
                            <div class="exam-date">Published: June 21, 2024</div>
                            <button class="take-exam-btn">Take Exam</button>
                        </div>
                    </a>
                </div>

                <!-- Exam Card 2 -->
                <div class="notification-item" data-category="drug-inspector" data-subject="all-subjects" data-date="2024-06-17">
                    <a href="single-exam.php" class="exam-card">
                        <div class="exam-header">
                            <div class="exam-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4 class="exam-title">Drug Inspector Practice Test</h4>
                        </div>
                        <div class="exam-meta">
                            <div class="exam-meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>100 Questions</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>2 hours</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-book"></i>
                                <span>All Subjects</span>
                            </div>
                        </div>
                        <div class="exam-footer">
                            <div class="exam-date">Published: June 17, 2024</div>
                            <button class="take-exam-btn">Take Exam</button>
                        </div>
                    </a>
                </div>

                <!-- Exam Card 3 -->
                <div class="notification-item" data-category="pharmacist" data-subject="state-level" data-date="2024-06-14">
                    <a href="single-exam.php" class="exam-card">
                        <div class="exam-header">
                            <div class="exam-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4 class="exam-title">Pharmacist Grade II Mock Exam</h4>
                        </div>
                        <div class="exam-meta">
                            <div class="exam-meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>80 Questions</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>90 minutes</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-star"></i>
                                <span>State Level</span>
                            </div>
                        </div>
                        <div class="exam-footer">
                            <div class="exam-date">Published: June 14, 2024</div>
                            <button class="take-exam-btn">Take Exam</button>
                        </div>
                    </a>
                </div>

                <!-- Exam Card 4 -->
                <div class="notification-item" data-category="niper" data-subject="entrance" data-date="2024-06-12">
                    <a href="single-exam.php" class="exam-card">
                        <div class="exam-header">
                            <div class="exam-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4 class="exam-title">NIPER JEE Entrance Mock Test</h4>
                        </div>
                        <div class="exam-meta">
                            <div class="exam-meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>150 Questions</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>3.5 hours</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Entrance</span>
                            </div>
                        </div>
                        <div class="exam-footer">
                            <div class="exam-date">Published: June 12, 2024</div>
                            <button class="take-exam-btn">Take Exam</button>
                        </div>
                    </a>
                </div>

                <!-- Exam Card 5 -->
                <div class="notification-item" data-category="assistant-pharmacist" data-subject="basic-pharmacy" data-date="2024-06-10">
                    <a href="single-exam.php" class="exam-card">
                        <div class="exam-header">
                            <div class="exam-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4 class="exam-title">Assistant Pharmacist Practice Exam</h4>
                        </div>
                        <div class="exam-meta">
                            <div class="exam-meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>75 Questions</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>75 minutes</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-flask"></i>
                                <span>Basic Pharmacy</span>
                            </div>
                        </div>
                        <div class="exam-footer">
                            <div class="exam-date">Published: June 10, 2024</div>
                            <button class="take-exam-btn">Take Exam</button>
                        </div>
                    </a>
                </div>

                <!-- Exam Card 6 -->
                <div class="notification-item" data-category="staff-nurse" data-subject="general-nursing" data-date="2024-06-08">
                    <a href="single-exam.php" class="exam-card">
                        <div class="exam-header">
                            <div class="exam-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4 class="exam-title">Staff Nurse Competitive Test</h4>
                        </div>
                        <div class="exam-meta">
                            <div class="exam-meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>100 Questions</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>2 hours</span>
                            </div>
                            <div class="exam-meta-item">
                                <i class="fas fa-atom"></i>
                                <span>General Nursing</span>
                            </div>
                        </div>
                        <div class="exam-footer">
                            <div class="exam-date">Published: June 8, 2024</div>
                            <button class="take-exam-btn">Take Exam</button>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Pagination Section -->
            <div class="pagination-section">
                <div class="pagination">
                    <a href="#" class="pagination-btn prev-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                    <div class="pagination-numbers" id="paginationNumbers">
                        <a href="#" class="pagination-number active" data-page="1">1</a>
                        <a href="#" class="pagination-number" data-page="2">2</a>
                        <a href="#" class="pagination-number" data-page="3">3</a>
                        <a href="#" class="pagination-number" data-page="4">4</a>
                        <a href="#" class="pagination-number" data-page="5">5</a>
                    </div>
                    <a href="#" class="pagination-btn next-btn" id="nextBtn">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="pagination-info">
                    <span id="paginationInfo">Showing 1-6 of 30 exams</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Banner Ad 2 - After Exams List -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner2.jpeg" alt="Advertisement">
    </div>

<?php include 'footer.php'; ?>