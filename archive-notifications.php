<?php 
$page_title = "Notifications - DRX Pharma Academy";
$current_page = "notifications";
$additional_js = '
    <script>
        // Notifications counting and pagination functionality
        document.addEventListener("DOMContentLoaded", function() {
            // Count notifications functionality
            const notificationCards = document.querySelectorAll(".notification-card");
            const notificationsCount = document.getElementById("notificationsCount");
            const paginationInfo = document.getElementById("paginationInfo");
            
            if (notificationCards.length > 0) {
                if (notificationsCount) {
                    notificationsCount.textContent = `Showing all notifications`;
                }
                if (paginationInfo) {
                    paginationInfo.textContent = `Showing 1-6 of 25 notifications`;
                }
            }
            
            // Add click tracking for notification cards
            notificationCards.forEach(card => {
                card.addEventListener("click", function(e) {
                    console.log("Notification clicked:", this.querySelector(".notification-title").textContent);
                });
            });
            
            // Pagination functionality
            const paginationNumbers = document.querySelectorAll(".pagination-number");
            const prevBtn = document.getElementById("prevBtn");
            const nextBtn = document.getElementById("nextBtn");
            
            paginationNumbers.forEach(link => {
                link.addEventListener("click", function(e) {
                    e.preventDefault();
                    paginationNumbers.forEach(l => l.classList.remove("active"));
                    this.classList.add("active");
                    
                    // Update pagination info
                    const currentPage = parseInt(this.textContent);
                    const startItem = (currentPage - 1) * 6 + 1;
                    const endItem = Math.min(currentPage * 6, 25);
                    
                    if (paginationInfo) {
                        paginationInfo.textContent = `Showing ${startItem}-${endItem} of 25 notifications`;
                    }
                    
                    // Update prev/next button styles
                    if (prevBtn) {
                        if (currentPage === 1) {
                            prevBtn.style.opacity = "0.5";
                            prevBtn.style.pointerEvents = "none";
                        } else {
                            prevBtn.style.opacity = "1";
                            prevBtn.style.pointerEvents = "auto";
                        }
                    }
                    
                    if (nextBtn) {
                        if (currentPage === 5) {
                            nextBtn.style.opacity = "0.5";
                            nextBtn.style.pointerEvents = "none";
                        } else {
                            nextBtn.style.opacity = "1";
                            nextBtn.style.pointerEvents = "auto";
                        }
                    }
                    
                    // Scroll to top of notifications
                    document.querySelector(".notifications-list-section").scrollIntoView({
                        behavior: "smooth"
                    });
                });
            });
            
            // Previous/Next button functionality
            if (prevBtn) {
                prevBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    const activePage = document.querySelector(".pagination-number.active");
                    const currentPage = parseInt(activePage.textContent);
                    if (currentPage > 1) {
                        activePage.classList.remove("active");
                        const prevPage = document.querySelector(`[data-page="${currentPage - 1}"]`);
                        if (prevPage) {
                            prevPage.classList.add("active");
                            prevPage.click();
                        }
                    }
                });
            }
            
            if (nextBtn) {
                nextBtn.addEventListener("click", function(e) {
                    e.preventDefault();
                    const activePage = document.querySelector(".pagination-number.active");
                    const currentPage = parseInt(activePage.textContent);
                    if (currentPage < 5) {
                        activePage.classList.remove("active");
                        const nextPage = document.querySelector(`[data-page="${currentPage + 1}"]`);
                        if (nextPage) {
                            nextPage.classList.add("active");
                            nextPage.click();
                        }
                    }
                });
            }
            
            // Initialize first page state
            if (prevBtn) {
                prevBtn.style.opacity = "0.5";
                prevBtn.style.pointerEvents = "none";
            }
            
            // Add keyboard navigation support
            document.addEventListener("keydown", function(e) {
                if (e.key === "ArrowLeft") {
                    if (prevBtn && prevBtn.style.pointerEvents !== "none") {
                        prevBtn.click();
                    }
                } else if (e.key === "ArrowRight") {
                    if (nextBtn && nextBtn.style.pointerEvents !== "none") {
                        nextBtn.click();
                    }
                }
            });
        });
    </script>';
include 'header.php'; 
?>

    <!-- Page Header Section -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    Latest Notifications
                </h1>
                <p class="page-subtitle">Stay updated with the latest exam notifications, announcements, and important updates</p>
            </div>
        </div>
    </section>

    <!-- Banner Ad 1 - After Title Section -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner3.jpeg" alt="Advertisement">
    </div>

    <!-- Notifications List Section -->
    <section class="notifications-list-section">
        <div class="container">
            <div class="notifications-count">
                <span id="notificationsCount">Showing all notifications</span>
            </div>
            
            <div class="notifications-grid" id="notificationsList">
                <a href="single-notification.php" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">173/2025 - Driver Gr.II (LDV) Driver Cum Office Attendant (LDV) in Various Notification</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 20, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.php" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">171/2025 - Driver Gr.II (HDV) (Ex-servicemen only) in NCC/Sainik Welfare</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 18, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.php" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">169/2025 - Assistant Pharmacist in Health Services Department</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 15, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.php" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">167/2025 - Laboratory Assistant Grade II in Various Departments</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 12, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.php" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">165/2025 - Staff Nurse Grade II in Health Services Department</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 10, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>

                <a href="single-notification.php" class="notification-card">
                    <div class="notification-header">
                        <div class="notification-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="notification-title">163/2025 - Pharmacist Grade II in Ayurveda Medical Education Department</h3>
                    </div>
                    <div class="notification-footer">
                        <span class="notification-date">
                            <i class="fas fa-calendar"></i> Published: June 8, 2024
                        </span>
                        <span class="notification-link">Read More</span>
                    </div>
                </a>
            </div>

            <!-- Pagination Section -->
            <div class="pagination-section">
                <div class="pagination">
                    <a href="#" class="pagination-btn prev-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                    <div class="pagination-numbers" id="paginationNumbers">
                        <a href="#" class="pagination-number active" data-page="1">1</a>
                        <a href="#" class="pagination-number" data-page="2">2</a>
                        <a href="#" class="pagination-number" data-page="3">3</a>
                        <a href="#" class="pagination-number" data-page="4">4</a>
                        <a href="#" class="pagination-number" data-page="5">5</a>
                    </div>
                    <a href="#" class="pagination-btn next-btn" id="nextBtn">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="pagination-info">
                    <span id="paginationInfo">Showing 1-6 of 25 notifications</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Banner Ad 2 - After Notifications List -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner2.jpeg" alt="Advertisement">
    </div>

<?php include 'footer.php'; ?>