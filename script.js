document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navMobile = document.getElementById('navMobile');
    const navMobileOverlay = document.getElementById('navMobileOverlay');
    const navMobileClose = document.getElementById('navMobileClose');
    const menuIcon = document.getElementById('menuIcon');
    const searchBtn = document.getElementById('searchBtn');
    const mobileSearchContainer = document.getElementById('mobileSearchContainer');
    const mobileSearchClose = document.getElementById('mobileSearchClose');

    function openMobileMenu() {
        navMobile.classList.add('active');
        navMobileOverlay.classList.add('active');
        document.body.classList.add('menu-open');
        // Only toggle icon
        menuIcon.classList.remove('fa-bars');
        menuIcon.classList.add('fa-times');
    }

    function closeMobileMenu() {
        navMobile.classList.remove('active');
        navMobileOverlay.classList.remove('active');
        document.body.classList.remove('menu-open');
        // Only toggle icon
        menuIcon.classList.remove('fa-times');
        menuIcon.classList.add('fa-bars');
    }

    function toggleMobileMenu() {
        if (navMobile.classList.contains('active')) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    }

    // Mobile menu button click
    mobileMenuBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleMobileMenu();
    });

    // Mobile menu close button click
    if (navMobileClose) {
        navMobileClose.addEventListener('click', function(e) {
            e.stopPropagation();
            closeMobileMenu();
        });
    }

    // Close mobile menu when clicking on a navigation link
    navMobile.addEventListener('click', function(e) {
        if (e.target.tagName === 'A' && !e.target.classList.contains('whatsapp-btn')) {
            setTimeout(closeMobileMenu, 150);
        }
    });

    // Close mobile menu when clicking on overlay
    navMobileOverlay.addEventListener('click', closeMobileMenu);

    // Close mobile menu with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && navMobile.classList.contains('active')) {
            closeMobileMenu();
        }
    });

    // Prevent scrolling when menu is open
    document.addEventListener('touchmove', function(e) {
        if (document.body.classList.contains('menu-open')) {
            e.preventDefault();
        }
    }, { passive: false });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && navMobile.classList.contains('active')) {
            closeMobileMenu();
        }
    });

    // Enhanced Mobile Search Functionality
    const mobileSearchOverlay = document.getElementById('mobileSearchOverlay');
    const mobileSearchBack = document.getElementById('mobileSearchBack');
    const mobileSearchInput = document.getElementById('mobileSearchInput');
    const mobileSearchClear = document.getElementById('mobileSearchClear');
    const mobileSearchSuggestions = document.getElementById('mobileSearchSuggestions');

    function openMobileSearch() {
        if (mobileSearchOverlay) {
            mobileSearchOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            // Focus on input after animation
            setTimeout(() => {
                if (mobileSearchInput) {
                    mobileSearchInput.focus();
                }
            }, 300);
        }
    }

    function closeMobileSearch() {
        if (mobileSearchOverlay) {
            mobileSearchOverlay.classList.remove('active');
            document.body.style.overflow = '';
            if (mobileSearchInput) {
                mobileSearchInput.value = '';
                mobileSearchInput.blur();
            }
            if (mobileSearchClear) {
                mobileSearchClear.classList.remove('visible');
            }
        }
    }

    // Search button click
    if (searchBtn) {
        searchBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openMobileSearch();
        });
    }

    // Search back button click
    if (mobileSearchBack) {
        mobileSearchBack.addEventListener('click', function(e) {
            e.preventDefault();
            closeMobileSearch();
        });
    }

    // Search input functionality
    if (mobileSearchInput && mobileSearchClear) {
        mobileSearchInput.addEventListener('input', function() {
            if (this.value.length > 0) {
                mobileSearchClear.classList.add('visible');
            } else {
                mobileSearchClear.classList.remove('visible');
            }
        });

        mobileSearchClear.addEventListener('click', function() {
            mobileSearchInput.value = '';
            mobileSearchClear.classList.remove('visible');
            mobileSearchInput.focus();
        });
    }

    // Search suggestions click
    if (mobileSearchSuggestions) {
        mobileSearchSuggestions.addEventListener('click', function(e) {
            const suggestionItem = e.target.closest('.search-suggestion-item');
            if (suggestionItem) {
                const suggestionText = suggestionItem.querySelector('span').textContent;
                if (mobileSearchInput) {
                    mobileSearchInput.value = suggestionText;
                    mobileSearchInput.focus();
                }
            }
        });
    }

    // Close search with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileSearchOverlay && mobileSearchOverlay.classList.contains('active')) {
            closeMobileSearch();
        }
    });

    // Prevent body scroll when search is open
    if (mobileSearchOverlay) {
        mobileSearchOverlay.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });
    }
});