<?php
$page_title = "Previous Question Papers - DRX Pharma Academy";
$current_page = "previous-questions";
$additional_css = '
<style>
@media (max-width: 768px) {
    #questionsList {
        grid-template-columns: 1fr !important;
    }
}
</style>';
include 'header.php';
?>

    <!-- Page Header Section -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    Previous Question Papers
                </h1>
                <p class="page-subtitle">Access comprehensive collection of previous year question papers for all competitive exams</p>
            </div>
        </div>
    </section>

    <!-- Banner Ad 1 - After Title Section -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner3.jpeg" alt="Advertisement">
    </div>

    <!-- Previous Questions List Section -->
    <section class="materials-section">
        <div class="container">
            <div class="notifications-count">
                <span id="questionsCount">Showing all question papers</span>
            </div>
            
            <div class="materials-grid" id="questionsList" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; max-width: 100%; margin: 0 auto;">
                <!-- Question Paper Card 1 -->
                <a href="single-previous-question.php" class="material-card" data-category="gpat" data-year="2024" data-subject="full-syllabus">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>GPAT 2024 Question Paper - Graduate Pharmacy Aptitude Test with Complete Solutions</h4>
                        <p>Graduate Pharmacy Aptitude Test - Complete paper with solutions</p>
                        <span class="material-meta">National Entrance Exam • February 2024</span>
                    </div>
                </a>

                <!-- Question Paper Card 2 -->
                <a href="single-previous-question.php" class="material-card" data-category="gpat" data-year="2023" data-subject="full-syllabus">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>GPAT 2023 Question Paper - Graduate Pharmacy Aptitude Test Previous Year Solved Paper</h4>
                        <p>Graduate Pharmacy Aptitude Test - Previous year solved paper</p>
                        <span class="material-meta">National Entrance Exam • February 2023</span>
                    </div>
                </a>

                <!-- Question Paper Card 3 -->
                <a href="single-previous-question.php" class="material-card" data-category="niper" data-year="2024" data-subject="entrance">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>NIPER JEE 2024 Question Paper - National Institute of Pharmaceutical Education Entrance Exam</h4>
                        <p>National Institute of Pharmaceutical Education entrance exam</p>
                        <span class="material-meta">National Entrance Exam • May 2024</span>
                    </div>
                </a>

                <!-- Question Paper Card 4 -->
                <a href="single-previous-question.php" class="material-card" data-category="drug-inspector" data-year="2024" data-subject="state-level">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>Drug Inspector 2024 - Kerala PSC Pharmaceutical Inspector Examination Question Paper</h4>
                        <p>Kerala PSC Drug Inspector examination paper</p>
                        <span class="material-meta">State Level Exam • March 2024</span>
                    </div>
                </a>

                <!-- Question Paper Card 5 -->
                <a href="single-previous-question.php" class="material-card" data-category="pharmacist" data-year="2024" data-subject="state-level">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>Pharmacist Grade II 2024 - Kerala Public Service Commission Previous Year Question Paper</h4>
                        <p>Kerala Public Service Commission Pharmacist exam</p>
                        <span class="material-meta">State Level Exam • April 2024</span>
                    </div>
                </a>

                <!-- Question Paper Card 6 -->
                <a href="single-previous-question.php" class="material-card" data-category="gpat" data-year="2022" data-subject="full-syllabus">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>GPAT 2022 Question Paper - Graduate Pharmacy Aptitude Test with Detailed Answer Key</h4>
                        <p>Graduate Pharmacy Aptitude Test with detailed solutions</p>
                        <span class="material-meta">National Entrance Exam • February 2022</span>
                    </div>
                </a>

                <!-- Question Paper Card 7 -->
                <a href="single-previous-question.php" class="material-card" data-category="niper" data-year="2023" data-subject="entrance">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>NIPER JEE 2023 Question Paper - National Institute Pharmaceutical Education Research Entrance</h4>
                        <p>National Institute entrance exam - All subjects covered</p>
                        <span class="material-meta">National Entrance Exam • May 2023</span>
                    </div>
                </a>

                <!-- Question Paper Card 8 -->
                <a href="single-previous-question.php" class="material-card" data-category="drug-inspector" data-year="2023" data-subject="state-level">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>Drug Inspector 2023 - Tamil Nadu Public Service Commission Pharmaceutical Inspector Exam</h4>
                        <p>Tamil Nadu Public Service Commission Drug Inspector exam</p>
                        <span class="material-meta">State Level Exam • September 2023</span>
                    </div>
                </a>

                <!-- Question Paper Card 9 -->
                <a href="single-previous-question.php" class="material-card" data-category="pharmacist" data-year="2023" data-subject="state-level">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>Pharmacist Grade II 2023 - Tamil Nadu Public Service Commission Previous Year Paper</h4>
                        <p>Tamil Nadu PSC Pharmacist examination paper</p>
                        <span class="material-meta">State Level Exam • June 2023</span>
                    </div>
                </a>

                <!-- Question Paper Card 10 -->
                <a href="single-previous-question.php" class="material-card" data-category="staff-nurse" data-year="2024" data-subject="health-services">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>Staff Nurse Grade II 2024 - Kerala PSC Health Services Department Question Paper</h4>
                        <p>Health Services Department Staff Nurse exam</p>
                        <span class="material-meta">State Level Exam • January 2024</span>
                    </div>
                </a>

                <!-- Question Paper Card 11 -->
                <a href="single-previous-question.php" class="material-card" data-category="lab-technician" data-year="2024" data-subject="state-level">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>Laboratory Assistant Grade II 2024 - Kerala PSC Health Department Previous Question Paper</h4>
                        <p>Laboratory Assistant Grade II examination paper</p>
                        <span class="material-meta">State Level Exam • February 2024</span>
                    </div>
                </a>

                <!-- Question Paper Card 12 -->
                <a href="single-previous-question.php" class="material-card" data-category="gpat" data-year="2021" data-subject="full-syllabus">
                    <div class="material-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="material-content">
                        <h4>GPAT 2021 Question Paper - Graduate Pharmacy Aptitude Test Complete Solved Paper</h4>
                        <p>Graduate Pharmacy Aptitude Test - Complete solved paper</p>
                        <span class="material-meta">National Entrance Exam • February 2021</span>
                    </div>
                </a>
            </div>

            <!-- Pagination Section -->
            <div class="pagination-section">
                <div class="pagination">
                    <a href="#" class="pagination-btn prev-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                    <div class="pagination-numbers" id="paginationNumbers">
                        <a href="#" class="pagination-number active" data-page="1">1</a>
                        <a href="#" class="pagination-number" data-page="2">2</a>
                        <a href="#" class="pagination-number" data-page="3">3</a>
                        <a href="#" class="pagination-number" data-page="4">4</a>
                        <a href="#" class="pagination-number" data-page="5">5</a>
                    </div>
                    <a href="#" class="pagination-btn next-btn" id="nextBtn">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="pagination-info">
                    <span id="paginationInfo">Showing 1-12 of 60 question papers</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Banner Ad 2 - After Questions List -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner2.jpeg" alt="Advertisement">
    </div>

<?php 
$additional_js = '
<script>
// Initialize previous questions
document.addEventListener(\'DOMContentLoaded\', function() {
    const questionsCount = document.getElementById(\'questionsCount\');
    const allQuestions = document.querySelectorAll(\'.material-card\');
    
    // Set initial count
    questionsCount.textContent = \'Showing all question papers\';

    // Pagination functionality
    const paginationNumbers = document.querySelectorAll(\'.pagination-number\');
    const prevBtn = document.getElementById(\'prevBtn\');
    const nextBtn = document.getElementById(\'nextBtn\');
    const paginationInfo = document.getElementById(\'paginationInfo\');
    let currentPage = 1;
    const totalPages = 5;

    function updatePagination() {
        // Update pagination numbers
        paginationNumbers.forEach(btn => {
            btn.classList.remove(\'active\');
            if (parseInt(btn.dataset.page) === currentPage) {
                btn.classList.add(\'active\');
            }
        });

        // Update prev/next buttons
        prevBtn.classList.toggle(\'disabled\', currentPage === 1);
        nextBtn.classList.toggle(\'disabled\', currentPage === totalPages);

        // Update pagination info
        const startItem = (currentPage - 1) * 12 + 1;
        const endItem = Math.min(currentPage * 12, 60);
        paginationInfo.textContent = `Showing ${startItem}-${endItem} of 60 question papers`;
    }

    // Pagination number click handlers
    paginationNumbers.forEach(btn => {
        btn.addEventListener(\'click\', function(e) {
            e.preventDefault();
            currentPage = parseInt(this.dataset.page);
            updatePagination();
            // Scroll to top of questions section
            document.querySelector(\'.materials-section\').scrollIntoView({ behavior: \'smooth\' });
        });
    });

    // Previous button click handler
    prevBtn.addEventListener(\'click\', function(e) {
        e.preventDefault();
        if (currentPage > 1) {
            currentPage--;
            updatePagination();
            document.querySelector(\'.materials-section\').scrollIntoView({ behavior: \'smooth\' });
        }
    });

    // Next button click handler
    nextBtn.addEventListener(\'click\', function(e) {
        e.preventDefault();
        if (currentPage < totalPages) {
            currentPage++;
            updatePagination();
            document.querySelector(\'.materials-section\').scrollIntoView({ behavior: \'smooth\' });
        }
    });

    // Initialize pagination
    updatePagination();

    // Question paper card click functionality
    const questionCards = document.querySelectorAll(\'.material-card\');
    
    questionCards.forEach(card => {
        card.addEventListener(\'click\', function(e) {
            e.preventDefault();
            const questionTitle = this.querySelector(\'h4\').textContent;
            
            // Add visual feedback
            this.style.transform = \'scale(0.98)\';
            setTimeout(() => {
                this.style.transform = \'\';
            }, 150);
            
            // Navigate to single previous question page
            window.location.href = \'single-previous-question.php\';
        });
    });

});
</script>';
include 'footer.php';
?>