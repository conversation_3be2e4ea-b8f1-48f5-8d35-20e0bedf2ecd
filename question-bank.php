<?php
$page_title = "Question Bank - DRX Pharma Academy";
$current_page = "question-bank";
$additional_css = '
<style>
.search-bar {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}
.search-input {
    width: 100%;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s ease;
}
.search-input:focus {
    border-color: #007bff;
}
.filter-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}
.filter-tab {
    padding: 10px 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}
.filter-tab.active, .filter-tab:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}
.category-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.category-card:hover {
    border: 1px solid #667eea;
    text-decoration: none;
    color: inherit;
}
.category-icon {
    width: 50px;
    height: 50px;
    background: #f0f4ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
.category-icon i {
    color: #667eea;
    font-size: 20px;
}
.category-content {
    flex: 1;
}
.category-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 6px 0;
    line-height: 1.3;
}
.category-content p {
    font-size: 13px;
    color: #666;
    margin: 0 0 6px 0;
    line-height: 1.4;
}
.question-count {
    font-size: 12px;
    color: #667eea;
    font-weight: 600;
}

.search-stats-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .category-grid {
        grid-template-columns: 1fr;
    }
    .search-stats-container {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    .search-stats-container .search-bar {
        width: 100%;
    }
    .search-stats-container .stats-display {
        text-align: center;
        justify-content: center;
    }
}
</style>';
include 'header.php';
?>

    <!-- Page Header Section -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">
                    Question Bank
                </h1>
                <p class="page-subtitle">Browse thousands of questions organized by subjects and difficulty levels</p>
            </div>
        </div>
    </section>

    <!-- Banner Ad 1 - After Title Section -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner3.jpeg" alt="Advertisement">
    </div>

    <!-- Question Bank Section -->
    <section class="materials-section">
        <div class="container">
            <!-- Search Bar with Stats -->
            <div class="search-stats-container">
                <div class="search-bar" style="flex: 1; margin: 0;">
                    <input type="text" class="search-input" placeholder="Search categories by name or description..." id="searchInput">
                </div>
                <div class="stats-display" style="display: flex; align-items: center; gap: 10px; white-space: nowrap;">
                    <span style="font-size: 16px; font-weight: 600; color: #667eea;">2,902</span>
                    <span style="color: #666; font-size: 14px;">Total Questions</span>
                </div>
            </div>


            <!-- Categories Grid -->
            <div class="category-grid" id="categoriesGrid">
                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmacology</h4>
                        <p>Drug actions, mechanisms, and therapeutic uses</p>
                        <span class="question-count"><span class="count">485</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmaceutics</h4>
                        <p>Dosage forms and drug delivery systems</p>
                        <span class="question-count"><span class="count">342</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmaceutical Chemistry</h4>
                        <p>Medicinal chemistry and drug design</p>
                        <span class="question-count"><span class="count">398</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmacognosy</h4>
                        <p>Natural products and herbal medicines</p>
                        <span class="question-count"><span class="count">267</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmaceutical Analysis</h4>
                        <p>Quality control and analytical methods</p>
                        <span class="question-count"><span class="count">289</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Clinical Pharmacy</h4>
                        <p>Patient care and pharmaceutical services</p>
                        <span class="question-count"><span class="count">234</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Biochemistry</h4>
                        <p>Biological processes and biomolecules</p>
                        <span class="question-count"><span class="count">198</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmaceutical Microbiology</h4>
                        <p>Microbiology and sterile preparations</p>
                        <span class="question-count"><span class="count">156</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmaceutical Jurisprudence</h4>
                        <p>Drug laws and pharmacy regulations</p>
                        <span class="question-count"><span class="count">145</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pathophysiology</h4>
                        <p>Disease mechanisms and pathology</p>
                        <span class="question-count"><span class="count">167</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Pharmaceutical Mathematics</h4>
                        <p>Calculations and pharmaceutical math</p>
                        <span class="question-count"><span class="count">123</span> <span class="label">Questions</span></span>
                    </div>
                </a>

                <a href="archive-question-bank.php" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="category-content">
                        <h4>Biostatistics</h4>
                        <p>Statistical methods in pharmacy research</p>
                        <span class="question-count"><span class="count">98</span> <span class="label">Questions</span></span>
                    </div>
                </a>
            </div>

        </div>
    </section>

    <!-- Banner Ad 2 - After Content -->
    <div class="banner-ad banner-ad-large">
        <img src="images/banner/banner2.jpeg" alt="Advertisement">
    </div>

<?php 
$additional_js = '
<script>
// Search functionality
document.addEventListener(\'DOMContentLoaded\', function() {
    const categoryCards = document.querySelectorAll(\'.category-card\');
    const searchInput = document.getElementById(\'searchInput\');


    // Search functionality for both search inputs
    function performSearch(searchTerm) {
        categoryCards.forEach(card => {
            const title = card.querySelector(\'h4\').textContent.toLowerCase();
            const description = card.querySelector(\'p\').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                card.style.display = \'flex\';
            } else {
                card.style.display = \'none\';
            }
        });
    }
    
    // Main search input
    searchInput.addEventListener(\'input\', function() {
        const searchTerm = this.value.toLowerCase();
        performSearch(searchTerm);
    });

    // Card click handlers
    categoryCards.forEach(card => {
        card.addEventListener(\'click\', function(e) {
            e.preventDefault();
            const categoryTitle = this.querySelector(\'.category-title\').textContent;
            alert(`Opening ${categoryTitle} question bank...`);
            // Here you would navigate to the specific category page
        });
    });
});
</script>';
include 'footer.php';
?>