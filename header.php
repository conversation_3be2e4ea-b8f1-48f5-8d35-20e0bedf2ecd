<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'DRX Pharma Academy - Pharmacy Competitive Exam Portal'; ?></title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css?v=<?php echo time(); ?>">
    <?php if(isset($additional_css)) echo $additional_css; ?>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="container">
            <!-- Mobile Header Layout -->
            <div class="header-content mobile-header">
                <!-- Left: Menu <PERSON>ton -->
                <div class="header-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Open navigation menu">
                        <i class="fas fa-bars" id="menuIcon"></i>
                    </button>
                </div>

                <!-- Center: Logo -->
                <div class="header-center">
                    <div class="logo">
                        <img src="images/logo/logo.png" alt="DRX Pharma Academy">
                    </div>
                </div>

                <!-- Right: Search Button -->
                <div class="header-right">
                    <button class="search-btn" id="searchBtn" aria-label="Search">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- Desktop Header Layout -->
            <div class="header-content desktop-header">
                <div class="logo">
                    <img src="images/logo/logo.png" alt="DRX Pharma Academy">
                </div>
                <nav class="nav desktop-nav">
                    <a href="index.php" <?php echo (isset($current_page) && $current_page == 'home') ? 'class="active"' : ''; ?>>Home</a>
                    <a href="archive-study-materials.php" <?php echo (isset($current_page) && $current_page == 'study-materials') ? 'class="active"' : ''; ?>>Study Materials</a>
                    <a href="archive-exams.php" <?php echo (isset($current_page) && $current_page == 'exams') ? 'class="active"' : ''; ?>>Exams</a>
                    <a href="archive-notifications.php" <?php echo (isset($current_page) && $current_page == 'notifications') ? 'class="active"' : ''; ?>>Notifications</a>
                    <a href="about.php" <?php echo (isset($current_page) && $current_page == 'about') ? 'class="active"' : ''; ?>>About</a>
                </nav>
                <a href="#" class="whatsapp-btn desktop-whatsapp">
                    <i class="fab fa-whatsapp"></i> Join WhatsApp
                </a>
            </div>

            <!-- Full Screen Mobile Search Overlay -->
            <div class="mobile-search-overlay" id="mobileSearchOverlay">
                <div class="mobile-search-container">
                    <div class="mobile-search-header">
                        <button class="mobile-search-back" id="mobileSearchBack" aria-label="Close search">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <div class="mobile-search-input-container">
                            <input type="text" class="mobile-search-input" id="mobileSearchInput" placeholder="Search for study materials, exams, notes..." autocomplete="off">
                            <button class="mobile-search-clear" id="mobileSearchClear" aria-label="Clear search">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mobile-search-suggestions" id="mobileSearchSuggestions">
                        <div class="search-suggestion-item">
                            <i class="fas fa-search"></i>
                            <span>GPAT preparation</span>
                        </div>
                        <div class="search-suggestion-item">
                            <i class="fas fa-search"></i>
                            <span>Pharmacology notes</span>
                        </div>
                        <div class="search-suggestion-item">
                            <i class="fas fa-search"></i>
                            <span>Drug inspector exam</span>
                        </div>
                        <div class="search-suggestion-item">
                            <i class="fas fa-search"></i>
                            <span>Previous year papers</span>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="nav-mobile" id="navMobile">
                <div class="nav-mobile-header">
                    <div class="nav-mobile-logo">
                        <i class="fas fa-graduation-cap"></i> DRX Pharma
                    </div>
                    <button class="nav-mobile-close" id="navMobileClose" aria-label="Close navigation menu">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <a href="index.php" <?php echo (isset($current_page) && $current_page == 'home') ? 'class="active"' : ''; ?>>
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="archive-study-materials.php" <?php echo (isset($current_page) && $current_page == 'study-materials') ? 'class="active"' : ''; ?>>
                    <i class="fas fa-book"></i> Study Materials
                </a>
                <a href="archive-exams.php" <?php echo (isset($current_page) && $current_page == 'exams') ? 'class="active"' : ''; ?>>
                    <i class="fas fa-file-alt"></i> Mock Exams
                </a>
                <a href="archive-notifications.php" <?php echo (isset($current_page) && $current_page == 'notifications') ? 'class="active"' : ''; ?>>
                    <i class="fas fa-bell"></i> Notifications
                </a>
                <a href="about.php" <?php echo (isset($current_page) && $current_page == 'about') ? 'class="active"' : ''; ?>>
                    <i class="fas fa-info-circle"></i> About Us
                </a>
                <div class="whatsapp-container">
                    <a href="#" class="whatsapp-btn">
                        <i class="fab fa-whatsapp"></i> Join WhatsApp Group
                    </a>
                </div>
            </nav>
        </div>
    </header>
    <div class="nav-mobile-overlay" id="navMobileOverlay"></div>